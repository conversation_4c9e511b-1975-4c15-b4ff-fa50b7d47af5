use thiserror::Error;

#[derive(E<PERSON><PERSON>, Debug)]
pub enum TicketServiceError {
    #[error("Database error: {0}")]
    DatabaseError(#[from] sqlx::Error),

    #[error("Ticket not found")]
    NotFound,

    #[error("Invalid ticket data: {0}")]
    InvalidData(String),

    #[error("Smartcontract error transaction: {0}")]
    SmartContractError(String),

    #[error("Invalid ticket data: {0}")]
    InvalidTicketData(String)
}
