use thiserror::Error;

use super::ticket_service_error::TicketServiceError;

#[derive(E<PERSON><PERSON>, Debug)]
pub enum OrchestratorError {
    #[error("Smart contract error: {0}")]
    SmartContractError(String),

    #[error("Ticket service error: {0}")]
    TicketServiceError(#[from] TicketServiceError),

    #[error("Invalid event data: {0}")]
    #[allow(dead_code)]
    InvalidEventData(String),

    #[error("Operation failed: {0}")]
    OperationFailed(String),
}
