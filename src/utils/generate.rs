#[allow(dead_code)]
pub fn generate_random_seed() -> u32 {
    use std::collections::hash_map::DefaultHasher;
    use std::hash::{Hash, Hasher};

    let now = std::time::SystemTime::now()
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap()
        .as_nanos();

    let mut hasher = DefaultHasher::new();
    now.hash(&mut hasher);
    (hasher.finish() & 0xFFFFFFFF) as u32 // Берем только младшие 32 бита
}
