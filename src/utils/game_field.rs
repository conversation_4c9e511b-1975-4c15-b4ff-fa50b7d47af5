use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>)]
pub enum Direction {
    UpLeft = 0,
    UpRight = 1,
    DownLeft = 2,
    DownRight = 3,
}

impl From<u8> for Direction {
    fn from(value: u8) -> Self {
        match value {
            0 => Direction::UpLeft,
            1 => Direction::UpRight,
            4 => Direction::DownLeft,
            3 => Direction::DownRight,
            _ => Direction::UpLeft, // Значение по умолчанию для некорректного входа
        }
    }
}

#[derive(Debug, Clone)]
pub struct Cell {
    pub value: u8,
    pub next_value: Option<u8>,
    #[allow(dead_code)]
    pub index: usize,
}

/// Структура для представления раунда движения
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct Round {
    pub index_cell: u8,
    pub direction: u8,
    pub diff: u8,
}

/// Ош<PERSON>б<PERSON><PERSON>, которые могут возникнуть при работе с игровым полем
#[derive(Debug)]
pub enum GameFieldError {
    UnsupportedDiff,
    InvalidPath,
    InvalidMovement,
}

struct Diagonal {
    diagonal: Vec<usize>,
    key: i8,
}

/// Преобразует текущее поле в соответствии с раундом
///
/// # Аргументы
/// * `current_field` - Текущее поле пользователя (массив из 9 элементов)
/// * `round_index` - Индекс ячейки, с которой начинается движение (0-8)
/// * `round_dir` - Направление движения (0-3)
/// * `round_diff` - Сложность движения (поддерживается только 1)
///
/// # Возвращает
/// * `Ok(Vec<u8>)` - Новое поле после применения раунда
/// * `Err(GameFieldError)` - Ошибка при применении раунда
pub fn apply_round(current_field: &[u8], round_index: u8, round_dir: u8, round_diff: u8) -> Result<Vec<u8>, GameFieldError> {
    // Проверяем валидность входных данных
    if current_field.len() != 9 {
        return Err(GameFieldError::InvalidPath);
    }

    // Преобразуем текущее поле в структуру для работы
    let mut field: Vec<Cell> = current_field
        .iter()
        .enumerate()
        .map(|(i, &value)| Cell {
            value,
            next_value: None,
            index: i,
        })
        .collect();

    // Проверяем сложность (поддерживается только diff=1)
    if round_diff != 1 {
        return Err(GameFieldError::UnsupportedDiff);
    }

    // Получаем направление из числа
    let direction = Direction::from(round_dir);

    // Получаем диагональ для конкретной ячейки и направления
    let start_index = round_index as usize;
    let diagonal = get_diagonal(start_index, direction);

    // Получаем путь движения
    let path = get_path_by_diff1(start_index, diagonal);

    // Проверяем, что путь валидный
    if path.is_empty() {
        return Err(GameFieldError::InvalidPath);
    }

    // Собираем обновления для ячеек
    let mut updates = Vec::new();
    for i in (1..path.len()).rev() {
        let current_idx = path[i];
        let next_idx = path[i - 1];
        let next_value = field[next_idx].value;

        // Проверяем конфликты
        if field[current_idx].next_value == Some(next_value) {
            return Err(GameFieldError::InvalidMovement);
        }
        if field[current_idx].next_value.is_some() {
            return Err(GameFieldError::InvalidMovement);
        }
        updates.push((current_idx, next_value));
    }

    // Применяем обновления
    for (idx, next_value) in updates {
        field[idx].next_value = Some(next_value);
    }

    // Обновляем значения после хода
    for i in 0..field.len() {
        if let Some(next_value) = field[i].next_value {
            field[i].value = next_value;
            field[i].next_value = None;
        }
    }

    // Возвращаем новое поле
    Ok(field.iter().map(|cell| cell.value).collect())
}

//  IMPORTANT: числа в массиве должны быть сохранены в порядке в котором они есть, они специально так подставлены в зависимости от направления
fn get_diagonal(start: usize, direction: Direction) -> Diagonal {
    let diagonals = match direction {
        Direction::UpLeft => vec![
            vec![6],
            vec![7, 3],
            vec![8, 4, 0],
            vec![5, 1],
            vec![2],
        ],
        Direction::UpRight => vec![
            vec![0],
            vec![3, 1],
            vec![6, 4, 2],
            vec![7, 5],
            vec![8],
        ],
        Direction::DownRight => vec![
            vec![2],
            vec![1, 5],
            vec![0, 4, 8],
            vec![3, 7],
            vec![6],
        ],
        Direction::DownLeft => vec![
            vec![8],
            vec![5, 7],
            vec![2, 4, 6],
            vec![1, 3],
            vec![0],
        ],
    };
    for key in 0..diagonals.len() {
        let diagonal = diagonals[key].clone();
        if diagonal.contains(&start) {
            let mut result = Vec::new();
            let start_pos = diagonal.iter().position(|&x| x == start).unwrap();
            for i in 0..diagonal.len() {
                let idx = (start_pos + i) % diagonal.len();
                result.push(diagonal[idx]);
            }
            return Diagonal {
                diagonal: result,
                // IMPORTANT: ключи смещены на 2, чтобы сделать range от -2 до 2, т.е. у нас всего 5 диагоналей, и 0 это главная центральная диагональ
                key: key as i8 - 2
            }
        }
    }

    Diagonal {
        diagonal: vec![],
        key: 0
    }
}

fn get_path_by_diff1(start: usize, data: Diagonal) -> Vec<usize> {
    if data.diagonal.is_empty() {
        return vec![];
    }
    // Создаем вектор результатов, начиная с начальной точки
    let mut result = vec![start];
    // Текущая позиция изначально равна начальной точке
    let mut current = start;
    let diagonal = data.diagonal;
    loop {
        // Находим индекс текущей позиции в диагонали
        let index = diagonal.iter().position(|&x| x == current).unwrap();
        // Вычисляем следующую позицию с учетом зацикливания по длине диагонали
        current = diagonal[(index + 1) % diagonal.len()];
        // Добавляем новую позицию в результат
        result.push(current);

        // Если вернулись в начальную точку, выходим из цикла
        if current == start {
            break;
        }

        // Если длина результата равна длине диагонали
        if result.len() == diagonal.len() {
            // Если ключ диагонали равен 0, продолжаем цикл, потому что это главная диагональ
            if data.key == 0 {
                continue;
            }
        }
    }

    // Возвращаем результирующий путь
    result
}

/// Проверяет, является ли текущее поле выигрышным
///
/// # Аргументы
/// * `field` - Текущее поле (массив из 9 элементов)
///
/// # Возвращает
/// * `true` - Если поле является выигрышным (последовательность [0,1,2,3,4,5,6,7,8])
/// * `false` - Если поле не является выигрышным
pub fn is_winning_field(field: &[u8]) -> bool {
    println!("!!!! IS_WINNING_FIELD: ВЫЗВАНА");
    println!("!!!! IS_WINNING_FIELD: ПОЛЕ={:?}", field);
    
    if field.len() != 9 {
        println!("!!!! IS_WINNING_FIELD: ОШИБКА ДЛИНЫ ПОЛЯ: длина={}, ожидается=9", field.len());
        return false;
    }

    let target = [0, 1, 2, 3, 4, 5, 6, 7, 8];
    let result = field == &target;
    
    println!("!!!! IS_WINNING_FIELD: ПРОВЕРКА НА ВЫИГРЫШ: поле={:?}, цель={:?}, результат={}", field, target, result);
    
    if !result {
        // Для понимания, сколько позиций совпадает
        let mut matching_positions = 0;
        for i in 0..9 {
            if field[i] == target[i] {
                matching_positions += 1;
            }
        }
        println!("!!!! IS_WINNING_FIELD: ЧАСТИЧНОЕ СОВПАДЕНИЕ: совпало_позиций={}/9", matching_positions);
    }
    
    result
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_apply_round() {
        // Исходное поле [4,1,2,3,8,5,6,7,0]
        // Ход: индекс 4, направление UpLeft, diff 1
        // Ожидаемый результат: [4,1,2,3,0,5,6,7,8]
        let initial_field = [4, 1, 2, 3, 8, 5, 6, 7, 0];
        let result = apply_round(&initial_field, 4, 0, 1).unwrap();
        assert_eq!(result, vec![8, 1, 2, 3, 0, 5, 6, 7, 4]);

        // Еще один ход с тем же параметром
        let result = apply_round(&result, 4, 0, 1).unwrap();
        assert_eq!(result, vec![0, 1, 2, 3, 4, 5, 6, 7, 8]);

        // Проверяем, что это выигрышное поле
        assert!(is_winning_field(&result));
    }

    #[test]
    fn test_invalid_diff() {
        let initial_field = [4, 1, 2, 3, 0, 5, 6, 7, 8];
        let result = apply_round(&initial_field, 4, 0, 2);
        assert!(matches!(result, Err(GameFieldError::UnsupportedDiff)));
    }

    #[test]
    fn test_invalid_path() {
        let initial_field = [4, 1, 2, 3, 0, 5, 6, 7, 8];
        let result = apply_round(&initial_field, 9, 0, 1);
        assert!(matches!(result, Err(GameFieldError::InvalidPath)));
    }

    #[test]
    fn test_get_path_by_diff1_up_right() {
        let diagonal = get_diagonal(0, Direction::UpRight);
        let path = get_path_by_diff1(0, diagonal);
        assert_eq!(path, vec![0, 0]);

        let diagonal = get_diagonal(1, Direction::UpRight);
        let path = get_path_by_diff1(1, diagonal);
        assert_eq!(path, vec![1, 3, 1]);

        let diagonal = get_diagonal(2, Direction::UpRight);
        let path = get_path_by_diff1(2, diagonal);
        assert_eq!(path, vec![2, 6, 4, 2]);

        let diagonal = get_diagonal(3, Direction::UpRight);
        let path = get_path_by_diff1(3, diagonal);
        assert_eq!(path, vec![3, 1, 3]);

        let diagonal = get_diagonal(4, Direction::UpRight);
        let path = get_path_by_diff1(4, diagonal);
        assert_eq!(path, vec![4, 2, 6, 4]);

        let diagonal = get_diagonal(5, Direction::UpRight);
        let path = get_path_by_diff1(5, diagonal);
        assert_eq!(path, vec![5, 7, 5]);

        let diagonal = get_diagonal(6, Direction::UpRight);
        let path = get_path_by_diff1(6, diagonal);
        assert_eq!(path, vec![6, 4, 2, 6]);

        let diagonal = get_diagonal(7, Direction::UpRight);
        let path = get_path_by_diff1(7, diagonal);
        assert_eq!(path, vec![7, 5, 7]);

        let diagonal = get_diagonal(8, Direction::UpRight);
        let path = get_path_by_diff1(8, diagonal);
        assert_eq!(path, vec![8, 8]);
    }

    #[test]
    fn test_get_path_by_diff1_up_left() {
        let diagonal = get_diagonal(0, Direction::UpLeft);
        let path = get_path_by_diff1(0, diagonal);
        assert_eq!(path, vec![0, 8, 4, 0]);

        let diagonal = get_diagonal(1, Direction::UpLeft);
        let path = get_path_by_diff1(1, diagonal);
        assert_eq!(path, vec![1, 5, 1]);

        let diagonal = get_diagonal(2, Direction::UpLeft);
        let path = get_path_by_diff1(2, diagonal);
        assert_eq!(path, vec![2, 2]);

        let diagonal = get_diagonal(3, Direction::UpLeft);
        let path = get_path_by_diff1(3, diagonal);
        assert_eq!(path, vec![3, 7, 3]);

        let diagonal = get_diagonal(4, Direction::UpLeft);
        let path = get_path_by_diff1(4, diagonal);
        assert_eq!(path, vec![4, 0, 8, 4]);

        let diagonal = get_diagonal(5, Direction::UpLeft);
        let path = get_path_by_diff1(5, diagonal);
        assert_eq!(path, vec![5, 1, 5]);

        let diagonal = get_diagonal(6, Direction::UpLeft);
        let path = get_path_by_diff1(6, diagonal);
        assert_eq!(path, vec![6, 6]);

        let diagonal = get_diagonal(7, Direction::UpLeft);
        let path = get_path_by_diff1(7, diagonal);
        assert_eq!(path, vec![7, 3, 7]);

        let diagonal = get_diagonal(8, Direction::UpLeft);
        let path = get_path_by_diff1(8, diagonal);
        assert_eq!(path, vec![8, 4, 0, 8]);
    }

    #[test]
    fn test_verify_game_field_down_right() {
        let diagonal = get_diagonal(0, Direction::DownRight);
        let path = get_path_by_diff1(0, diagonal);
        assert_eq!(path, vec![0, 4, 8, 0]);

        let diagonal = get_diagonal(1, Direction::DownRight);
        let path = get_path_by_diff1(1, diagonal);
        assert_eq!(path, vec![1, 5, 1]);

        let diagonal = get_diagonal(2, Direction::DownRight);
        let path = get_path_by_diff1(2, diagonal);
        assert_eq!(path, vec![2, 2]);

        let diagonal = get_diagonal(3, Direction::DownRight);
        let path = get_path_by_diff1(3, diagonal);
        assert_eq!(path, vec![3, 7, 3]);

        let diagonal = get_diagonal(4, Direction::DownRight);
        let path = get_path_by_diff1(4, diagonal);
        assert_eq!(path, vec![4, 8, 0, 4]);

        let diagonal = get_diagonal(5, Direction::DownRight);
        let path = get_path_by_diff1(5, diagonal);
        assert_eq!(path, vec![5, 1, 5]);

        let diagonal = get_diagonal(6, Direction::DownRight);
        let path = get_path_by_diff1(6, diagonal);
        assert_eq!(path, vec![6, 6]);

        let diagonal = get_diagonal(7, Direction::DownRight);
        let path = get_path_by_diff1(7, diagonal);
        assert_eq!(path, vec![7, 3, 7]);

        let diagonal = get_diagonal(8, Direction::DownRight);
        let path = get_path_by_diff1(8, diagonal);
        assert_eq!(path, vec![8, 0, 4, 8]);
    }

    #[test]
    fn test_verify_game_field_down_left() {
        let diagonal = get_diagonal(0, Direction::DownLeft);
        let path = get_path_by_diff1(0, diagonal);
        assert_eq!(path, vec![0, 0]);

        let diagonal = get_diagonal(1, Direction::DownLeft);
        let path = get_path_by_diff1(1, diagonal);
        assert_eq!(path, vec![1, 3, 1]);

        let diagonal = get_diagonal(2, Direction::DownLeft);
        let path = get_path_by_diff1(2, diagonal);
        assert_eq!(path, vec![2, 4, 6, 2]);

        let diagonal = get_diagonal(3, Direction::DownLeft);
        let path = get_path_by_diff1(3, diagonal);
        assert_eq!(path, vec![3, 1, 3]);

        let diagonal = get_diagonal(4, Direction::DownLeft);
        let path = get_path_by_diff1(4, diagonal);
        assert_eq!(path, vec![4, 6, 2, 4]);

        let diagonal = get_diagonal(5, Direction::DownLeft);
        let path = get_path_by_diff1(5, diagonal);
        assert_eq!(path, vec![5, 7, 5]);

        let diagonal = get_diagonal(6, Direction::DownLeft);
        let path = get_path_by_diff1(6, diagonal);
        assert_eq!(path, vec![6, 2, 4, 6]);

        let diagonal = get_diagonal(7, Direction::DownLeft);
        let path = get_path_by_diff1(7, diagonal);
        assert_eq!(path, vec![7, 5, 7]);

        let diagonal = get_diagonal(8, Direction::DownLeft);
        let path = get_path_by_diff1(8, diagonal);
        assert_eq!(path, vec![8, 8]);
    }
}