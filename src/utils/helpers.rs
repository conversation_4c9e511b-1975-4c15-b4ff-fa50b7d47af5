use std::env;

use chrono::{DateTime, Duration, Utc};
use rand::Rng;
use serde_json::Value;

use crate::db::entities::types::DurationTimeType;

#[allow(dead_code)]
pub fn get_struct_name<T>(_val: &T) -> &'static str {
    std::any::type_name::<T>().split("::").last().unwrap_or("Unknown")
}

pub fn get_env_var(key: &str) -> String {
    match env::var(key) {
        Ok(val) => {
            println!("Successfully got {} = {}", key, val);
            val
        },
        Err(e) => {
            println!("Failed to get {}: {:?}", key, e);
            panic!("{} must be set", key)
        }
    }
}

#[allow(dead_code)]
pub fn is_valid_sequence(user_field: [u8; 9]) -> bool {
    user_field == [0, 1, 2, 3, 4, 5, 6, 7, 8]
}

// вспомогательная функция для извлечения поля из JSON
pub fn extract_field_from_json(field_json: &Value) -> Result<Vec<u8>, String> {
    match field_json {
        Value::Array(arr) => {
            let mut result = Vec::with_capacity(arr.len());
            for value in arr {
                if let Some(num) = value.as_u64() {
                    result.push(num as u8);
                } else {
                    return Err("Field value is not a number".to_string());
                }
            }
            Ok(result)
        },
        _ => Err("Field is not an array".to_string()),
    }
}

pub fn future_date_depends(timestamp: DateTime<Utc>, duration_type: DurationTimeType) -> DateTime<Utc> {
    let duration = match duration_type {
        DurationTimeType::MIN_1 => Duration::minutes(1),
        DurationTimeType::HOUR_1 => Duration::hours(1),
        DurationTimeType::DAY_1 => Duration::days(1),
        DurationTimeType::WEEK_1 => Duration::weeks(1),
        DurationTimeType::MONTH_1 => Duration::days(30),
        DurationTimeType::MONTH_3 => Duration::days(30 * 3),
        DurationTimeType::MONTH_6 => Duration::days(30 * 6),
        DurationTimeType::YEAR_1 => Duration::days(365),
        DurationTimeType::YEAR_3 => Duration::days(365 * 3),
        DurationTimeType::YEAR_5 => Duration::days(365 * 5),
    };
    timestamp + duration
}

fn generate_random_seed_u32() -> u32 {
    let mut rng = rand::rng();
    rng.random::<u32>() & 0x7FFFFFFF
}

pub fn generate_multiple_seeds(count: usize) -> Vec<u32> {
    (0..count).map(|_| generate_random_seed_u32()).collect()
}

#[allow(dead_code)]
pub fn check_if_creator_is_admin(creator_pub_key: &str) -> bool {
    let admin_key = get_env_var("SERVER_ADMIN_PUBLIC_KEY");
    println!("[DEBUG] Comparing creator key with admin key: creator={}, admin={}",
             creator_pub_key, admin_key);
    creator_pub_key == admin_key
}