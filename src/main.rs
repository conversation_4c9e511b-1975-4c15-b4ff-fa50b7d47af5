mod app;
mod db;
mod errors;
mod events;
mod field;
mod orchestrators;
mod routes;
mod services;
mod tests;
mod utils;

use std::sync::Arc;
use tokio::sync::RwLock;

use actix_web::{web, App, HttpServer};
use app::app_context::AppContext;
use dotenvy::dotenv;
use orchestrators::EventOrchestrator;
use services::pubsub_service::PubSubService;

use events::*;
use log::{error, info};
use utils::helpers::get_env_var;

#[actix_web::main]
async fn main() -> std::io::Result<()> {
    // Инициализация логгера
    env_logger::init();

    // Load environment variables from `.env` file.
    dotenv().ok();

    info!("Starting ticket service backend...");
    info!("All env vars: {:?}", std::env::vars().collect::<Vec<_>>());

    // Create a global application context.
    let app_context = AppContext::new();

    // Start the event listener.
    tokio::spawn(event_listener(app_context.clone()));

    info!("HTTP server starting on {}", get_env_var("SERVER_HOST"));

    // Start the HTTP server.
    HttpServer::new(move || {
        App::new()
            .configure(routes::init)
            .app_data(web::Data::new(app_context.clone()))
    })
    .bind(&get_env_var("SERVER_HOST"))?
    .run()
    .await
}

/// Starts the event listener.
async fn event_listener(app_context: AppContext) {
    let mut pubsub_service = PubSubService::new(
        &get_env_var("WS_BLOCKCAIN_HOST"),
        &get_env_var("PROGRAM_ID"),
    );
    let pub_sub_connaction = pubsub_service.connect().await.unwrap(); // Connect to the WebSocket server.

    info!(
        "Event listener started, connecting to {}",
        get_env_var("WS_BLOCKCAIN_HOST")
    );

    // Создаем оркестратор один раз для всех обработчиков
    let app_context_clone = app_context.clone();
    let orchestrator = Arc::new(RwLock::new(EventOrchestrator::new(
        app_context_clone.smartcontract_service.clone(),
        app_context_clone.ticket_service.clone(),
        app_context_clone.api_service.clone(),
        app_context_clone.random_service.clone(),
        app_context_clone.user_service.clone(),
    )));

    // Subscribe to initialized ticket event using the orchestrator
    let orchestrator_clone = orchestrator.clone();
    pub_sub_connaction
        .subscribe(move |event: TicketInitializedEvent| {
            let orchestrator = orchestrator_clone.clone();
            println!("!!!! ВАЖНЫЙ ЛОГ: БУДЕТ ВЫЗВАНА process_ticket_initialization В ОРКЕСТРАТОРЕ");
            tokio::spawn(async move {
                match orchestrator
                    .write()
                    .await
                    .process_ticket_initialization(event)
                    .await
                {
                    Ok(()) => info!("Successfully processed ticket initialization"),
                    Err(e) => error!("Failed to process ticket initialization: {}", e),
                }
            });
        })
        .await;

    // Subscribe to purchased ticket event
    let orchestrator_clone = orchestrator.clone();
    pub_sub_connaction
        .subscribe(move |event: TicketPurchasedEvent| {
            let orchestrator = orchestrator_clone.clone();
            println!("!!!! ВАЖНЫЙ ЛОГ: ПОЛУЧЕНО СОБЫТИЕ ПОКУПКИ БИЛЕТА: ticket_id={:?}, user={}, field={:?}",
                    event.ticket_id, event.user_id, event.user_field);
            println!("!!!! ВАЖНЫЙ ЛОГ: БУДЕТ ВЫЗВАНА process_ticket_purchased В ОРКЕСТРАТОРЕ");
            tokio::spawn(async move {
                match orchestrator
                    .write()
                    .await
                    .process_ticket_purchased(event)
                    .await
                {
                    Ok(()) => info!("Successfully processed ticket purchase"),
                    Err(e) => {
                        error!("Failed to process ticket purchase: {}", e);
                        println!("!!!! ВАЖНЫЙ ЛОГ: ОШИБКА ПРИ ОБРАБОТКЕ ПОКУПКИ БИЛЕТА: {}", e);
                    }
                }
            });
        })
        .await;

    // Подписка на событие вывода пассивного дохода
    let app_context_clone = app_context.clone();
    pub_sub_connaction
        .subscribe(move |event: PassiveIncomeWithdrawEvent| {
            let app_context_clone = app_context_clone.clone();
            tokio::spawn(async move {
                let ticket_service = app_context_clone.ticket_service.clone();
                let result = ticket_service
                    .write()
                    .await
                    .process_passive_income_withdrawal(event)
                    .await;
                if let Err(e) = result {
                    error!("Failed to process passive income withdrawal: {}", e);
                }
            });
        })
        .await;

    let app_context_clone = app_context.clone();
    pub_sub_connaction
        .subscribe(move |event: JackpotClaimEvent| {
            let app_context_clone = app_context_clone.clone();
            tokio::spawn(async move {
                let ticket_service = app_context_clone.ticket_service.clone();
                let result = ticket_service
                    .write()
                    .await
                    .process_jackpot_claim(event)
                    .await;
                if let Err(e) = result {
                    error!("Failed to process jackpot claim: {}", e);
                }
            });
        })
        .await;

    // Subscribe to jackpot weighted random value event
    let app_context_clone = app_context.clone();
    pub_sub_connaction
        .subscribe(move |event: JackpotWeightedRandomValueEvent| {
            let app_context_clone = app_context_clone.clone();
            tokio::spawn(async move {
                let user_service = app_context_clone.user_service.clone();
                let result = user_service.write().await.set_winner_by_random(event).await;
                if let Err(e) = result {
                    error!("Failed to process jackpot weighted random value: {}", e);
                }
            });
        })
        .await;

    // Subscribe to ticket lock event
    // let app_context_clone = app_context.clone();
    // pub_sub_connaction
    //     .subscribe(move |event: TicketLockedEvent| {
    //         let app_context_clone = app_context_clone.clone();
    //         tokio::spawn(async move {
    //             // let ticket_service = app_context_clone.ticket_service.clone();
    //             // let result = ticket_service.write().await.process_ticket_lock(event).await;
    //             // if let Err(e) = result {
    //             //     eprintln!("Failed to process ticket lock: {}", e);
    //             // }
    //         });
    //     })
    //     .await;

    // // Subscribe to ticket unlock event
    // let app_context_clone = app_context.clone();
    // pub_sub_connaction
    //     .subscribe(move |event: TicketUnlockedEvent| {
    //         let app_context_clone = app_context_clone.clone();
    //         tokio::spawn(async move {
    //             // let ticket_service = app_context_clone.ticket_service.clone();
    //             // let result = ticket_service.write().await.process_ticket_unlock(event).await;
    //             // if let Err(e) = result {
    //             //     eprintln!("Failed to process ticket unlock: {}", e);
    //             // }
    //         });
    //     })
    //     .await;

    // Регистрируем оставшиеся обработчики, использующие оркестратор
    let orchestrator_clone = orchestrator.clone();
    pub_sub_connaction
        .subscribe(move |event: TicketExpiredDetectedEvent| {
            let orchestrator = orchestrator_clone.clone();
            tokio::spawn(async move {
                match orchestrator
                    .write()
                    .await
                    .process_ticket_expired(event)
                    .await
                {
                    Ok(result) => info!("Successfully processed ticket expiration: {}", result),
                    Err(e) => error!("Failed to process ticket expiration: {}", e),
                }
            });
        })
        .await;

    let orchestrator_clone = orchestrator.clone();
    pub_sub_connaction
        .subscribe(move |event: TicketChunkCleanupEvent| {
            let orchestrator = orchestrator_clone.clone();
            tokio::spawn(async move {
                match orchestrator
                    .write()
                    .await
                    .process_chunk_cleanup(event)
                    .await
                {
                    Ok(result) => info!("Successfully processed chunk cleanup: {}", result),
                    Err(e) => error!("Failed to process chunk cleanup: {}", e),
                }
            });
        })
        .await;

    let orchestrator_clone = orchestrator.clone();
    pub_sub_connaction
        .subscribe(move |event: TicketCleanupEvent| {
            let orchestrator = orchestrator_clone.clone();
            tokio::spawn(async move {
                match orchestrator
                    .write()
                    .await
                    .process_ticket_cleanup(event)
                    .await
                {
                    Ok(result) => info!("Successfully processed ticket cleanup: {}", result),
                    Err(e) => error!("Failed to process ticket cleanup: {}", e),
                }
            });
        })
        .await;

    let orchestrator_clone = orchestrator.clone();
    pub_sub_connaction
        .subscribe(move |event: TicketArchivedEvent| {
            let orchestrator = orchestrator_clone.clone();
            tokio::spawn(async move {
                match orchestrator
                    .write()
                    .await
                    .process_ticket_archived(event)
                    .await
                {
                    Ok(result) => info!("Successfully processed ticket archived: {}", result),
                    Err(e) => error!("Failed to process ticket archived: {}", e),
                }
            });
        })
        .await;

    let orchestrator_clone = orchestrator.clone();
    pub_sub_connaction
        .subscribe(move |event: TicketWinnerEvent| {
            let orchestrator = orchestrator_clone.clone();
            tokio::spawn(async move {
                match orchestrator
                    .write()
                    .await
                    .process_ticket_winner(event)
                    .await
                {
                    Ok(result) => info!("Successfully processed ticket winner: {}", result),
                    Err(e) => error!("Failed to process ticket winner: {}", e),
                }
            });
        })
        .await;

    let orchestrator_clone = orchestrator.clone();
    pub_sub_connaction
        .subscribe(move |event: RandomResponseEvent| {
            let orchestrator = orchestrator_clone.clone();
            tokio::spawn(async move {
                match orchestrator
                    .write()
                    .await
                    .random_number_handler(event)
                    .await
                {
                    Ok(result) => info!("Successfully processed random number: {}", result),
                    Err(e) => error!("Failed to process random number: {}", e),
                }
            });
        })
        .await;

    // Start listening for events.
    info!("Starting event listener...");
    if let Err(e) = pub_sub_connaction.start_listen().await {
        error!("Failed to start listening: {}", e);
    }
}
