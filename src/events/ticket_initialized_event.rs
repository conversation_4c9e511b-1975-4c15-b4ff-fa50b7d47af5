use borsh::{BorshDeserialize, BorshSerialize};
use solana_sdk::pubkey::Pubkey;

#[derive(BorshDeserialize, BorshSerialize, Debug, Clone)]
pub struct TicketInitializedEvent {
    pub ticket_id: [u8; 16],
    pub admin: Pub<PERSON>,
    pub creator: Pubkey,
    pub price: u64,
    pub timestamp: u64,
    pub ticket_seed: u32,
    pub jackpot_seed: u32,
    pub first_chunk_seed: u32,
    pub ticket_end: u64,
    pub currency: String,
    pub url_logo: String,
    pub url_image: String,
}