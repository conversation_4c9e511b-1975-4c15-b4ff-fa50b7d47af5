use borsh::{BorshDeserialize, BorshSerialize};
use solana_sdk::pubkey::Pubkey;

#[derive(BorshDeserialize, BorshSerialize, Debug, Clone)]
pub struct TicketPurchasedEvent {
    pub ticket_id: [u8; 16],
    pub amount: u64,
    pub chunk_index: u64,
    pub participant_index: u64,
    pub timestamp: u64,
    pub jackpot_amount: u64,
    //pub jackpot_amounts: Option<Vec<(Pubkey, u64)>>,
    pub chunk_seed: u32,

    pub user_id: Pubkey,
    pub user_field: [u8; 9],

    pub round_index: u8,
    pub round_dir: u8,
    pub round_diff: u8,
}
