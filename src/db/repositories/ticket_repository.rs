use crate::db::entities::ticket::Ticket;
use super::repository::Repository;
use sqlx::{PgPool, <PERSON>rro<PERSON>};

#[derive(Clone)]
pub struct TicketRepository {
    pub pool: PgPool,
}

impl TicketRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }
    #[allow(dead_code)]
    pub async fn update_current_chunk_seed(&mut self, ticket_pubkey: String, new_chunk_seed: i64) -> Result<Ticket, Error> {
        sqlx::query_as::<_, Ticket>(
            r#"UPDATE "Ticket"
               SET "currentChunkSeed" = $1
               WHERE "pubkey" = $2
               RETURNING *"#
        )
            .bind(new_chunk_seed)
            .bind(ticket_pubkey)
            .fetch_one(&self.pool)
            .await
    }
    #[allow(dead_code)]
    pub async fn update_bank_amounts(
        &mut self,
        ticket_pubkey: String,
        main_bank_amount: f64,
        income_bank_amount: f64,
        col_bank_amount: f64
    ) -> Result<Ticket, Error> {
        sqlx::query_as::<_, Ticket>(
            r#"UPDATE "Ticket"
               SET "mainBankAmount" = $2,
                   "incomeBankAmount" = $3,
                   "colBankAmount" = $4
               WHERE pubkey = $1
               RETURNING *"#
        )
        .bind(ticket_pubkey)
        .bind(main_bank_amount)
        .bind(income_bank_amount)
        .bind(col_bank_amount)
        .fetch_one(&self.pool)
        .await
    }
}

impl Repository<Ticket, String> for TicketRepository {

    async fn create(&mut self, ticket: Ticket) -> Result<Ticket, Error> {
        sqlx::query_as::<_, Ticket>(
            r#"INSERT INTO "Ticket" (
                "uuid", "pubkey", "currency", "entryFee", "durationType", "duration",
                "endTime", "mainBankAmount", "incomeBankAmount", "colBankAmount",
                "totalPlayers", "createdAt", "currentState", "completedType", "urlLogo", "urlImage",
                "ticketSeed", "jackpotSeed", "firstChunkSeed", "currentChunkSeed", "winnerPubKey", "creatorPubKey",
                "isClaimed"
            )
            VALUES (
                $1, $2, $3::TEXT::"CurrencyType", $4, $5::TEXT::"DurationType",
                $6::TEXT::"DurationTimeType", $7, $8, $9, $10, $11, $12,
                $13::TEXT::"TicketState", $14::TEXT::"TicketCompletedType", $15, $16, $17, $18, $19, $20, $21, $22,
                $23
            )
            RETURNING *"#
        )
            .bind(ticket.uuid)
            .bind(ticket.pubkey)
            .bind(ticket.currency.to_string())
            .bind(ticket.entry_fee)
            .bind(ticket.duration_type.to_string())
            .bind(ticket.duration.as_ref().map(|v| v.to_string()))
            .bind(ticket.end_time)
            .bind(ticket.main_bank_amount)
            .bind(ticket.income_bank_amount)
            .bind(ticket.col_bank_amount)
            .bind(ticket.total_players)
            .bind(ticket.created_at)
            .bind(ticket.current_state.to_string())
            .bind(ticket.completed_type.as_ref().map(|v| v.to_string()))
            .bind(ticket.url_logo)
            .bind(ticket.url_image)
            .bind(ticket.ticket_seed)
            .bind(ticket.jackpot_seed)
            .bind(ticket.first_chunk_seed)
            .bind(ticket.current_chunk_seed)
            .bind(ticket.winner_pub_key)
            .bind(ticket.creator_pub_key)
            .bind(ticket.is_claimed)
            .fetch_one(&self.pool)
            .await
    }

    async fn find_by_id(&mut self, uuid: String) -> Result<Option<Ticket>, Error> {
        sqlx::query_as::<_, Ticket>(
            r#"SELECT * FROM "Ticket" WHERE "uuid" = $1"#
        )
        .bind(uuid)
        .fetch_optional(&self.pool)
        .await
    }

    async fn find_all(&mut self) -> Result<Vec<Ticket>, Error> {
        sqlx::query_as::<_, Ticket>(
            r#"SELECT * FROM "Ticket""#
        )
        .fetch_all(&self.pool)
        .await
    }

    async fn update(&mut self, ticket: Ticket) -> Result<Ticket, Error> {
        sqlx::query_as::<_, Ticket>(
            r#"UPDATE "Ticket"
            SET  "uuid" = $1,
                 "currency" = $2::TEXT::"CurrencyType",
                 "entryFee" = $3,
                 "durationType" = $4::TEXT::"DurationType",
                 "duration" = $5::TEXT::"DurationTimeType",
                 "endTime" = $6,
                 "mainBankAmount" = $7,
                 "incomeBankAmount" = $8,
                 "colBankAmount" = $9,
                 "totalPlayers" = $10,
                 "createdAt" = $11,
                 "currentState" = $12::TEXT::"TicketState",
                 "completedType" = $13::TEXT::"TicketCompletedType",
                 "urlLogo" = $14,
                 "urlImage" = $15,
                 "ticketSeed" = $16,
                 "jackpotSeed" = $17,
                 "firstChunkSeed" = $18,
                 "currentChunkSeed" = $19,
                 "winnerPubKey" = $20,
                 "creatorPubKey" = $21,
                 "isClaimed" = $22
            WHERE "pubkey" = $23
            RETURNING *"#
        )
            .bind(ticket.uuid)
            .bind(ticket.currency.to_string())
            .bind(ticket.entry_fee)
            .bind(ticket.duration_type.to_string())
            .bind(ticket.duration.as_ref().map(|v| v.to_string()))
            .bind(ticket.end_time)
            .bind(ticket.main_bank_amount)
            .bind(ticket.income_bank_amount)
            .bind(ticket.col_bank_amount)
            .bind(ticket.total_players)
            .bind(ticket.created_at)
            .bind(ticket.current_state.to_string())
            .bind(ticket.completed_type.as_ref().map(|v| v.to_string()))
            .bind(ticket.url_logo)
            .bind(ticket.url_image)
            .bind(ticket.ticket_seed)
            .bind(ticket.jackpot_seed)
            .bind(ticket.first_chunk_seed)
            .bind(ticket.current_chunk_seed)
            .bind(ticket.winner_pub_key)
            .bind(ticket.creator_pub_key)
            .bind(ticket.is_claimed)
            .bind(ticket.pubkey)
            .fetch_one(&self.pool)
            .await
    }


    async fn delete(&mut self, pubkey: String) -> Result<(), Error> {
        sqlx::query!(
            r#"DELETE FROM "Ticket" WHERE "pubkey" = $1"#,
            pubkey
        )
        .execute(&self.pool)
        .await
        .map(|_| ())
    }
}
