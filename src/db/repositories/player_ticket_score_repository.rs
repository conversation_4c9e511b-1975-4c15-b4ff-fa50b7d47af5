use crate::db::entities::player_ticket_score::{NewPlayerTicketScore, PlayerTicketScore};
use super::repository::Repository;
use chrono::{DateTime, Utc};
use sqlx::{PgPool, Error};

#[derive(Clone)]
pub struct PlayerTicketScoreRepository {
    pool: PgPool,
}

impl PlayerTicketScoreRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn create_new(&mut self, entity: NewPlayerTicketScore) -> Result<PlayerTicketScore, Error> {
        sqlx::query_as::<_, PlayerTicketScore>(
            r#"INSERT INTO "PlayerTicketScore" ("ticketPubkey", "userPubkey", score, "lastUpdated")
               VALUES ($1, $2, $3, $4)
               RETURNING *"#
        )
        .bind(entity.ticket_pubkey)
        .bind(entity.user_pubkey)
        .bind(entity.score)
        .bind(entity.last_updated)
        .fetch_one(&self.pool)
        .await
    }

    pub async fn update_player_score(&mut self, new_score: i32, updated_at: DateTime<Utc>, player_score_id: i32) -> Result<(), Error> {
        sqlx::query!(
            r#"UPDATE "PlayerTicketScore"
               SET score = $1, "lastUpdated" = $2
               WHERE id = $3"#,
            new_score,
            updated_at.naive_utc(),
            player_score_id
        )
        .execute(&self.pool)
        .await
        .map(|_| ())
    }

    pub async fn reset_player_score(&mut self, ticket_pubkey: String, user_pubkey: String, updated_at: DateTime<Utc>) -> Result<u64, Error> {
        let result = sqlx::query!(
            r#"UPDATE "PlayerTicketScore" SET score = 0, "lastUpdated" = $1
               WHERE "ticketPubkey" = $2 AND "userPubkey" = $3"#,
            updated_at.naive_utc(),
            ticket_pubkey,
            user_pubkey
        )
        .execute(&self.pool)
        .await?;
        
        Ok(result.rows_affected())
    }
    
    pub async fn find_by_user_and_ticket(&mut self, user_pubkey: String, ticket_pubkey: String) -> Result<Option<PlayerTicketScore>, Error> {
        sqlx::query_as::<_, PlayerTicketScore>(
            r#"SELECT * FROM "PlayerTicketScore"
               WHERE "userPubkey" = $1 AND "ticketPubkey" = $2"#
        )
            .bind(user_pubkey)
            .bind(ticket_pubkey)
            .fetch_optional(&self.pool)
            .await
    }

    pub async fn find_all_users_by_ticket(&mut self, ticket_pubkey: &str) -> Result<Vec<PlayerTicketScore>, Error> {
        sqlx::query_as::<_, PlayerTicketScore>(
            r#"SELECT * FROM "PlayerTicketScore" WHERE "ticketPubkey" = $1"#
        )
            .bind(ticket_pubkey)
            .fetch_all(&self.pool)
            .await
    }

}

impl Repository<PlayerTicketScore, i32> for PlayerTicketScoreRepository {

    async fn create(&mut self, entity: PlayerTicketScore) -> Result<PlayerTicketScore, Error> {
        sqlx::query_as::<_, PlayerTicketScore>(
            r#"INSERT INTO "PlayerTicketScore" ("ticketPubkey", "userPubkey", score, "lastUpdated")
               VALUES ($1, $2, $3, $4)
               RETURNING *"#
        )
        .bind(entity.ticket_pubkey)
        .bind(entity.user_pubkey)
        .bind(entity.score)
        .bind(entity.last_updated)
        .fetch_one(&self.pool)
        .await
    }

    async fn find_by_id(&mut self, id: i32) -> Result<Option<PlayerTicketScore>, Error> {
        sqlx::query_as::<_, PlayerTicketScore>(
            r#"SELECT * FROM "PlayerTicketScore" WHERE id = $1"#
        )
        .bind(id)
        .fetch_optional(&self.pool)
        .await
    }

    async fn find_all(&mut self) -> Result<Vec<PlayerTicketScore>, Error> {
        sqlx::query_as::<_, PlayerTicketScore>(
            r#"SELECT * FROM "PlayerTicketScore""#
        )
        .fetch_all(&self.pool)
        .await
    }
    
    async fn update(&mut self, entity: PlayerTicketScore) -> Result<PlayerTicketScore, Error> {
        sqlx::query_as::<_, PlayerTicketScore>(
            r#"UPDATE "PlayerTicketScore" 
               SET "ticketPubkey" = $1, "userPubkey" = $2, score = $3, "lastUpdated" = NOW() 
               WHERE id = $4
               RETURNING *"#
        )
        .bind(entity.ticket_pubkey)
        .bind(entity.user_pubkey)
        .bind(entity.score)
        .bind(entity.last_updated)
        .bind(entity.id)
        .fetch_one(&self.pool)
        .await
    }

    async fn delete(&mut self, id: i32) -> Result<(), sqlx::Error> {
        sqlx::query!(
            r#"DELETE FROM "PlayerTicketScore" WHERE id = $1"#,
            id
        )
        .execute(&self.pool)
        .await
        .map(|_| ())
    }
    
}