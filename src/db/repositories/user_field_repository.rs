use crate::db::entities::user_field::{NewUserField, UserField};
use super::repository::Repository;
use chrono::{DateTime, Utc};
use sqlx::{PgPool, Error};
use serde_json::Value;

#[derive(Clone)]
pub struct UserFieldRepository {
    pub pool: PgPool,
}

impl UserFieldRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn create_new(&mut self, entity: NewUserField) -> Result<UserField, Error> {
        sqlx::query_as::<_, UserField>(
            r#"INSERT INTO "UserField" ("userPubkey", "ticketPubkey", "initedField", "currentField", "updatedAt")
               VALUES ($1, $2, $3, $4, $5)
               RETURNING *"#
        )
        .bind(entity.user_pubkey)
        .bind(entity.ticket_pubkey)
        .bind(entity.inited_field)
        .bind(entity.current_field)
        .bind(entity.updated_at)
        .fetch_one(&self.pool)
        .await
    }

    pub async fn find_all_by_ticket_id(&mut self, ticket_pubkey: String) -> Result<Vec<UserField>, Error> {
        sqlx::query_as::<_, UserField>(
            r#"SELECT * FROM "UserField" WHERE "ticketPubkey" = $1"#
        )
            .bind(ticket_pubkey)
            .fetch_all(&self.pool)
            .await
    }

    pub async fn update_current_field(&mut self, new_field_json: Value, update_time: DateTime<Utc>, user_pubkey: String, ticket_pubkey: String) -> Result<UserField, Error> {
        let exists = sqlx::query_scalar!(
            r#"SELECT EXISTS(SELECT 1 FROM "UserField" WHERE "userPubkey" = $1 AND "ticketPubkey" = $2)"#,
            user_pubkey,
            ticket_pubkey
        )
            .fetch_one(&self.pool)
            .await?;

        println!("update_current_field:exists: {:?}", exists);
        if exists.unwrap_or(false) {
            sqlx::query_as::<_, UserField>(
                r#"UPDATE "UserField"
               SET "currentField" = $1, "updatedAt" = $2
               WHERE "userPubkey" = $3 AND "ticketPubkey" = $4
               RETURNING *"#
            )
                .bind(new_field_json)
                .bind(update_time)
                .bind(user_pubkey)
                .bind(ticket_pubkey)
                .fetch_one(&self.pool)
                .await
        } else {
            Err(Error::RowNotFound)
        }
    }
}

impl Repository<UserField, i32> for UserFieldRepository {

    async fn create(&mut self, entity: UserField) -> Result<UserField, Error> {
        sqlx::query_as::<_, UserField>(
            r#"INSERT INTO "UserField" ("userPubkey", "ticketPubkey", "initedField", "currentField", "updatedAt")
               VALUES ($1, $2, $3, $4, $5)
               RETURNING *"#
        )
        .bind(entity.user_pubkey)
        .bind(entity.ticket_pubkey)
        .bind(entity.inited_field)
        .bind(entity.current_field)
        .bind(entity.updated_at)
        .fetch_one(&self.pool)
        .await
    }

    async fn find_by_id(&mut self, id: i32) -> Result<Option<UserField>, Error> {
        sqlx::query_as::<_, UserField>(
            r#"SELECT * FROM "UserField" WHERE id = $1"#
        )
        .bind(id)
        .fetch_optional(&self.pool)
        .await
    }

    async fn find_all(&mut self) -> Result<Vec<UserField>, Error> {
        sqlx::query_as::<_, UserField>(
            r#"SELECT * FROM "UserField""#
        )
        .fetch_all(&self.pool)
        .await
    }

    async fn update(&mut self, entity: UserField) -> Result<UserField, Error> {
        sqlx::query_as::<_, UserField>(
            r#"UPDATE "UserField"
               SET "userPubkey" = $1, "ticketPubkey" = $2, "initedField" = $3, "currentField" = $4, "updatedAt" = $5
               WHERE id = $6
               RETURNING *"#
        )
        .bind(entity.user_pubkey)
        .bind(entity.ticket_pubkey)
        .bind(entity.inited_field)
        .bind(entity.current_field)
        .bind(entity.updated_at)
        .bind(entity.id)
        .fetch_one(&self.pool)
        .await
    }

    async fn delete(&mut self, id: i32) -> Result<(), Error> {
        sqlx::query!(
            r#"DELETE FROM "UserField" WHERE id = $1"#,
            id
        )
        .execute(&self.pool)
        .await
        .map(|_| ())
    }
}
