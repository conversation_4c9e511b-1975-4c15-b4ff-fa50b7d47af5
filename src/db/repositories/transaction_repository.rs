use crate::db::entities::transaction::{NewTransaction, Transaction};
use super::repository::Repository;
use sqlx::{PgPool, Error};

#[derive(Clone)]
pub struct TransactionRepository {
    pub pool: PgPool,
}

impl TransactionRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }
    
    pub async fn create_new(&mut self, transaction: NewTransaction) -> Result<Transaction, Error> {
        sqlx::query_as::<_, Transaction>(
            r#"INSERT INTO "Transaction" ("userPubkey", "ticketPubkey", "amount", "type", "createdAt") 
               VALUES ($1, $2, $3, $4::TEXT::"TransactionType", $5)
               RETURNING *"#
        )
        .bind(transaction.user_pubkey)
        .bind(transaction.ticket_pubkey)
        .bind(transaction.amount)
        .bind(transaction.transaction_type.to_string())
        .bind(transaction.created_at)
        .fetch_one(&self.pool)
        .await
    }
}

impl Repository<Transaction, i32> for TransactionRepository {

    async fn create(&mut self, transaction: Transaction) -> Result<Transaction, Error> {
        sqlx::query_as::<_, Transaction>(
            r#"INSERT INTO "Transaction" ("userPubkey", "ticketPubkey", "amount", "type", "createdAt") 
               VALUES ($1, $2, $3, $4::TEXT::"TransactionType", $5)
               RETURNING *"#
        )
        .bind(transaction.user_pubkey)
        .bind(transaction.ticket_pubkey)
        .bind(transaction.amount)
        .bind(transaction.transaction_type.to_string())
        .bind(transaction.created_at)
        .fetch_one(&self.pool)
        .await
    }

    async fn find_by_id(&mut self, id: i32) -> Result<Option<Transaction>, Error> {
        sqlx::query_as::<_, Transaction>(
            r#"SELECT * FROM "Transaction" WHERE id = $1"#
        )
        .bind(id)
        .fetch_optional(&self.pool)
        .await
    }

    async fn find_all(&mut self) -> Result<Vec<Transaction>, Error> {
        sqlx::query_as::<_, Transaction>(
            r#"SELECT * FROM "Transaction""#
        )
        .fetch_all(&self.pool)
        .await
    }

    async fn update(&mut self, transaction: Transaction) -> Result<Transaction, Error> {
        sqlx::query_as::<_, Transaction>(
            r#"UPDATE "Transaction" 
               SET  "userPubkey" = $1, 
                    "ticketPubkey" = $2, 
                    "amount" = $3, 
                    "type" = $4::TEXT::"TransactionType", 
                    "createdAt" = $5
               WHERE id = $6
               RETURNING *"#
        )
        .bind(transaction.user_pubkey)
        .bind(transaction.ticket_pubkey)
        .bind(transaction.amount)
        .bind(transaction.transaction_type.to_string())
        .bind(transaction.created_at)
        .bind(transaction.id)
        .fetch_one(&self.pool)
        .await
    }

    async fn delete(&mut self, id: i32) -> Result<(), Error> {
        sqlx::query!(
            r#"DELETE FROM "Transaction" WHERE id = $1"#,
            id
        )
        .execute(&self.pool)
        .await
        .map(|_| ())
    }
}
