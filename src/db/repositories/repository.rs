use sqlx::Error;

pub trait Repository<T, ID> {

    async fn create(&mut self, entity: T) -> Result<T, Error>;
    async fn find_by_id(&mut self, id: ID) -> Result<Option<T>, Error>;
    #[allow(dead_code)]
    async fn find_all(&mut self) -> Result<Vec<T>, Error>;
    async fn update(&mut self, entity: T) -> Result<T, Error>;
    #[allow(dead_code)]
    async fn delete(&mut self, id: ID) -> Result<(), Error>;
}
