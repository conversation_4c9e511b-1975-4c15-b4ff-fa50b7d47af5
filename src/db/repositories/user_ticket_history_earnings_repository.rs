use crate::db::entities::{types::EarningStatus, user_ticket_history_earnings::{NewUserTicketHistoryEarnings, UserTicketHistoryEarnings}};
use sqlx::{PgPool, Error};

#[derive(Clone)]
pub struct UserTicketHistoryEarningsRepository {
    pub pool: PgPool,
}

impl UserTicketHistoryEarningsRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn create_new(&mut self, entity: NewUserTicketHistoryEarnings) -> Result<UserTicketHistoryEarnings, Error> {
        sqlx::query_as::<_, UserTicketHistoryEarnings>(
            r#"INSERT INTO "UserTicketHistoryEarnings"
               ("userPubkey", "ticketPubkey", "amount", "totalAmount", "fromUserPubkey", "status", "createdAt", "transactionId")
               VALUES ($1, $2, $3, $4, $5, $6::TEXT::"EarningStatus", $7, $8)
               RETURNING *"#
        )
        .bind(entity.user_pubkey)
        .bind(entity.ticket_pubkey)
        .bind(entity.amount)
        .bind(entity.total_amount)
        .bind(entity.from_user_pubkey)
        .bind(entity.status.to_string())
        .bind(entity.created_at)
        .bind(entity.transaction_id)
        .fetch_one(&self.pool)
        .await
    }

    #[allow(dead_code)]
    pub async fn update_status_by_ticket(&mut self, ticket_pubkey: String, new_status: EarningStatus) -> Result<(), Error> {
        let exists = sqlx::query_scalar!(
            r#"SELECT EXISTS(SELECT 1 FROM "UserTicketHistoryEarnings" WHERE "ticketPubkey" = $1)"#,
            ticket_pubkey
        )
            .fetch_one(&self.pool)
            .await?;

        println!("update_status_by_ticket:exists: {:?}", exists);

        if exists.unwrap_or(false) {
            // Обновляем статус для всех записей данного билета
            sqlx::query(
                r#"UPDATE "UserTicketHistoryEarnings"
               SET "status" = $1::TEXT::"EarningStatus"
               WHERE "ticketPubkey" = $2
                RETURNING *"#
            )
                .bind(new_status.to_string())
                .bind(ticket_pubkey)
                .execute(&self.pool)
                .await?;

            Ok(())
        } else {
            Err(Error::RowNotFound)
        }
    }

    pub async fn get_total_amount_by_user_and_ticket(&mut self, user_pubkey: String, ticket_pubkey: String) -> Result<f64, Error> {
        let result = sqlx::query!(
            r#"SELECT SUM(amount) as total
               FROM "UserTicketHistoryEarnings"
               WHERE "userPubkey" = $1 AND "ticketPubkey" = $2 AND "status" = 'PENDING'::TEXT::"EarningStatus""#,
            user_pubkey,
            ticket_pubkey
        )
        .fetch_one(&self.pool)
        .await?;

        Ok(result.total.unwrap_or(0.0))
    }

    pub async fn update_earnings_status_to_paid(&mut self, ticket_pubkey: String, user_pubkey: String) -> Result<u64, Error> {
        let result = sqlx::query!(
            r#"UPDATE "UserTicketHistoryEarnings"
               SET "status" = 'PAID'::text::"EarningStatus"
               WHERE "ticketPubkey" = $1 AND "userPubkey" = $2 AND "status" = 'PENDING'::text::"EarningStatus""#,
            ticket_pubkey,
            user_pubkey
        )
        .execute(&self.pool)
        .await?;
        
        Ok(result.rows_affected())
    }
}