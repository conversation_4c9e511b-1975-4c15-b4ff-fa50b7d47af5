use crate::db::entities::random::{Random, NewRandom};
use super::repository::Repository;
use sqlx::{PgPool, Error};

#[derive(Clone)]
pub struct RandomRepository {
    pub pool: PgPool,
}

impl RandomRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn create_new(&mut self, new_random: NewRandom) -> Result<Random, Error> {
        sqlx::query_as::<_, Random>(
            r#"INSERT INTO "Random" ("ticketId", "hash", "isUsed", "userId", "txId", "createdAt") 
               VALUES ($1, $2, $3, $4, $5, NOW())
               RETURNING *"#
        )
        .bind(new_random.ticket_id)
        .bind(new_random.hash)
        .bind(false) // isUsed defaults to false
        .bind(new_random.user_id)
        .bind(new_random.tx_id)
        .fetch_one(&self.pool)
        .await
    }

    pub async fn find_by_ticket_id(&mut self, ticket_id: String) -> Result<Vec<Random>, Error> {
        sqlx::query_as::<_, Random>(
            r#"SELECT * FROM "Random" WHERE "ticketId" = $1 ORDER BY "createdAt" DESC"#
        )
        .bind(ticket_id)
        .fetch_all(&self.pool)
        .await
    }

    pub async fn find_by_user_id(&mut self, user_id: String) -> Result<Vec<Random>, Error> {
        sqlx::query_as::<_, Random>(
            r#"SELECT * FROM "Random" WHERE "userId" = $1 ORDER BY "createdAt" DESC"#
        )
        .bind(user_id)
        .fetch_all(&self.pool)
        .await
    }

    pub async fn find_by_hash(&mut self, hash: String) -> Result<Option<Random>, Error> {
        sqlx::query_as::<_, Random>(
            r#"SELECT * FROM "Random" WHERE "hash" = $1"#
        )
        .bind(hash)
        .fetch_optional(&self.pool)
        .await
    }

    pub async fn find_unused_by_ticket_id(&mut self, ticket_id: String) -> Result<Vec<Random>, Error> {
        sqlx::query_as::<_, Random>(
            r#"SELECT * FROM "Random" WHERE "ticketId" = $1 AND "isUsed" = false ORDER BY "createdAt" ASC"#
        )
        .bind(ticket_id)
        .fetch_all(&self.pool)
        .await
    }

    pub async fn mark_as_used(&mut self, id: i32) -> Result<Random, Error> {
        sqlx::query_as::<_, Random>(
            r#"UPDATE "Random" SET "isUsed" = true WHERE "id" = $1 RETURNING *"#
        )
        .bind(id)
        .fetch_one(&self.pool)
        .await
    }

    pub async fn find_by_ticket_and_user(&mut self, ticket_id: String, user_id: String) -> Result<Vec<Random>, Error> {
        sqlx::query_as::<_, Random>(
            r#"SELECT * FROM "Random" WHERE "ticketId" = $1 AND "userId" = $2 ORDER BY "createdAt" DESC"#
        )
        .bind(ticket_id)
        .bind(user_id)
        .fetch_all(&self.pool)
        .await
    }
}

impl Repository<Random, i32> for RandomRepository {

    async fn create(&mut self, random: Random) -> Result<Random, Error> {
        sqlx::query_as::<_, Random>(
            r#"INSERT INTO "Random" ("ticketId", "hash", "isUsed", "userId", "txId", "createdAt") 
               VALUES ($1, $2, $3, $4, $5, $6)
               RETURNING *"#
        )
        .bind(random.ticket_id)
        .bind(random.hash)
        .bind(random.is_used)
        .bind(random.user_id)
        .bind(random.tx_id)
        .bind(random.created_at)
        .fetch_one(&self.pool)
        .await
    }

    async fn find_by_id(&mut self, id: i32) -> Result<Option<Random>, Error> {
        sqlx::query_as::<_, Random>(
            r#"SELECT * FROM "Random" WHERE "id" = $1"#
        )
        .bind(id)
        .fetch_optional(&self.pool)
        .await
    }

    async fn find_all(&mut self) -> Result<Vec<Random>, Error> {
        sqlx::query_as::<_, Random>(
            r#"SELECT * FROM "Random" ORDER BY "createdAt" DESC"#
        )
        .fetch_all(&self.pool)
        .await
    }

    async fn update(&mut self, random: Random) -> Result<Random, Error> {
        sqlx::query_as::<_, Random>(
            r#"UPDATE "Random" 
               SET "ticketId" = $1, "hash" = $2, "isUsed" = $3, "userId" = $4, "txId" = $5, "createdAt" = $6
               WHERE "id" = $7
               RETURNING *"#
        )
        .bind(random.ticket_id)
        .bind(random.hash)
        .bind(random.is_used)
        .bind(random.user_id)
        .bind(random.tx_id)
        .bind(random.created_at)
        .bind(random.id)
        .fetch_one(&self.pool)
        .await
    }
    
    async fn delete(&mut self, id: i32) -> Result<(), Error> {
        sqlx::query!(
            r#"DELETE FROM "Random" WHERE "id" = $1"#,
            id
        )
        .execute(&self.pool)
        .await
        .map(|_| ())
    }
} 