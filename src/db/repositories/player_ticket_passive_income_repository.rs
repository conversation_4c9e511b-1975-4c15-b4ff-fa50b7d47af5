// src/db/repositories/player_ticket_passive_income_repository.rs
use crate::db::entities::player_ticket_passive_income::{PlayerTicketPassiveIncome, NewPlayerTicketPassiveIncome};
use super::repository::Repository;
use sqlx::{PgPool, Error};

#[derive(Clone)]
pub struct PlayerTicketPassiveIncomeRepository {
    pub pool: PgPool,
}

impl PlayerTicketPassiveIncomeRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn create_new(&mut self, entity: NewPlayerTicketPassiveIncome) -> Result<PlayerTicketPassiveIncome, Error> {
        sqlx::query_as::<_, PlayerTicketPassiveIncome>(
            r#"INSERT INTO "PlayerTicketPassiveIncome" ("userPubkey", "ticketPubkey", "currency", "income", "lastUpdated")
               VALUES ($1, $2, $3::TEXT::"CurrencyType", $4, $5)
               RETURNING *"#
        )
            .bind(entity.user_pubkey)
            .bind(entity.ticket_pubkey)
            .bind(entity.currency.to_string())
            .bind(entity.income)
            .bind(entity.last_updated)
            .fetch_one(&self.pool)
            .await
    }

    pub async fn update_income(&mut self, user_pubkey: String, ticket_pubkey: String, amount: f64) -> Result<PlayerTicketPassiveIncome, Error> {
        sqlx::query_as::<_, PlayerTicketPassiveIncome>(
            r#"UPDATE "PlayerTicketPassiveIncome"
               SET "income" = "income" + $1, "lastUpdated" = NOW()
               WHERE "userPubkey" = $2 AND "ticketPubkey" = $3
               RETURNING *"#
        )
            .bind(amount)
            .bind(user_pubkey)
            .bind(ticket_pubkey)
            .fetch_one(&self.pool)
            .await
    }

    pub async fn reset_income(&mut self, user_pubkey: String, ticket_pubkey: String) -> Result<PlayerTicketPassiveIncome, Error> {
        sqlx::query_as::<_, PlayerTicketPassiveIncome>(
            r#"UPDATE "PlayerTicketPassiveIncome"
               SET "income" = 0, "lastUpdated" = NOW()
               WHERE "userPubkey" = $1 AND "ticketPubkey" = $2
               RETURNING *"#
        )
            .bind(user_pubkey)
            .bind(ticket_pubkey)
            .fetch_one(&self.pool)
            .await
    }

    pub async fn get_income(&mut self, user_pubkey: String, ticket_pubkey: String) -> Result<Option<PlayerTicketPassiveIncome>, Error> {
        sqlx::query_as::<_, PlayerTicketPassiveIncome>(
            r#"SELECT * FROM "PlayerTicketPassiveIncome"
               WHERE "userPubkey" = $1 AND "ticketPubkey" = $2"#
        )
            .bind(user_pubkey)
            .bind(ticket_pubkey)
            .fetch_optional(&self.pool)
            .await
    }
}

impl Repository<PlayerTicketPassiveIncome, i32> for PlayerTicketPassiveIncomeRepository {
    // Стандартные методы CRUD...
    async fn create(&mut self, entity: PlayerTicketPassiveIncome) -> Result<PlayerTicketPassiveIncome, Error> {
        sqlx::query_as::<_, PlayerTicketPassiveIncome>(
            r#"INSERT INTO "PlayerTicketPassiveIncome" ("userPubkey", "ticketPubkey", "currency", "income", "lastUpdated")
               VALUES ($1, $2, $3::TEXT::"CurrencyType", $4, $5)
               RETURNING *"#
        )
            .bind(entity.user_pubkey)
            .bind(entity.ticket_pubkey)
            .bind(entity.currency.to_string())
            .bind(entity.income)
            .bind(entity.last_updated)
            .fetch_one(&self.pool)
            .await
    }

    async fn find_by_id(&mut self, id: i32) -> Result<Option<PlayerTicketPassiveIncome>, Error> {
        sqlx::query_as::<_, PlayerTicketPassiveIncome>(
            r#"SELECT * FROM "PlayerTicketPassiveIncome" WHERE id = $1"#
        )
            .bind(id)
            .fetch_optional(&self.pool)
            .await
    }

    async fn find_all(&mut self) -> Result<Vec<PlayerTicketPassiveIncome>, Error> {
        sqlx::query_as::<_, PlayerTicketPassiveIncome>(
            r#"SELECT * FROM "PlayerTicketPassiveIncome""#
        )
            .fetch_all(&self.pool)
            .await
    }

    async fn update(&mut self, entity: PlayerTicketPassiveIncome) -> Result<PlayerTicketPassiveIncome, Error> {
        sqlx::query_as::<_, PlayerTicketPassiveIncome>(
            r#"UPDATE "PlayerTicketPassiveIncome"
               SET "currency" = $1::TEXT::"CurrencyType",
                   "income" = $2,
                   "lastUpdated" = $3
               WHERE id = $4
               RETURNING *"#
        )
            .bind(entity.currency.to_string())
            .bind(entity.income)
            .bind(entity.last_updated)
            .bind(entity.id)
            .fetch_one(&self.pool)
            .await
    }

    async fn delete(&mut self, id: i32) -> Result<(), Error> {
        sqlx::query!(
            r#"DELETE FROM "PlayerTicketPassiveIncome" WHERE id = $1"#,
            id
        )
            .execute(&self.pool)
            .await
            .map(|_| ())
    }
}
