use crate::db::entities::{ticket_history::{NewTicketHistory, TicketHistory}, types::TicketHistoryActionType};
use super::repository::Repository;
use chrono::{DateTime, Utc};
use sqlx::{PgPool, Error};

#[derive(Clone)]
pub struct TicketHistoryRepository {
    pool: PgPool,
}

impl TicketHistoryRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn create_new(&mut self, history: NewTicketHistory) -> Result<TicketHistory, Error> {
        sqlx::query_as::<_, TicketHistory>(
            r#"INSERT INTO "TicketHistory" (
                "ticketPubkey",
                "userPubkey",
                "actionType",
                "actionTime",
                "transactionId"
            )
            VALUES ($1, $2, $3::TEXT::"TicketHistoryActionType", $4, $5)
            RETURNING *"#
        )
        .bind(history.ticket_pubkey)
        .bind(history.user_pubkey)
        .bind(history.action_type.to_string())
        .bind(history.action_time)
        .bind(history.transaction_id)
        .fetch_one(&self.pool)
        .await
    }

    pub async fn create_ticket_completion(&mut self, ticket_pubkey: String, winner_pubkey: String, action_time: DateTime<Utc>) -> Result<TicketHistory, Error> {
        let action_type = TicketHistoryActionType::COMPLETION; // Используем новый тип

        sqlx::query_as::<_, TicketHistory>(
            r#"INSERT INTO "TicketHistory" ("ticketPubkey", "userPubkey", "actionTime", "actionType")
               VALUES ($1, $2, $3, $4::TEXT::"TicketHistoryActionType")
               RETURNING *"#
        )
        .bind(ticket_pubkey)
        .bind(winner_pubkey)
        .bind(action_time.naive_utc())
        .bind(action_type.to_string())
        .fetch_one(&self.pool)
        .await
    }

    #[allow(dead_code)]
    pub async fn find_by_ticket_id(&mut self, ticket_pubkey: String) -> Result<Vec<TicketHistory>, Error> {
        sqlx::query_as::<_, TicketHistory>(
            r#"SELECT * FROM "TicketHistory" WHERE "ticketPubkey" = $1 ORDER BY "actionTime" DESC"#
        )
        .bind(ticket_pubkey)
        .fetch_all(&self.pool)
        .await
    }

    pub async fn find_completion(&mut self, ticket_pubkey: String, user_pubkey: String) -> Result<Option<TicketHistory>, Error> {
        sqlx::query_as::<_, TicketHistory>(
            r#"SELECT * FROM "TicketHistory"
               WHERE "ticketPubkey" = $1
               AND "userPubkey" = $2
               AND "actionType" = 'COMPLETION'"#
        )
        .bind(ticket_pubkey)
        .bind(user_pubkey)
        .fetch_optional(&self.pool)
        .await
    }
}

impl Repository<TicketHistory, i32> for TicketHistoryRepository {

    async fn create(&mut self, entity: TicketHistory) -> Result<TicketHistory, Error> {
        sqlx::query_as::<_, TicketHistory>(
            r#"INSERT INTO "TicketHistory" ("ticketPubkey", "userPubkey", "actionTime", "actionType")
               VALUES ($1, $2, $3, $4::TEXT::"TicketHistoryActionType")
               RETURNING *"#
        )
        .bind(entity.ticket_pubkey)
        .bind(entity.user_pubkey)
        .bind(entity.action_time)
        .bind(entity.action_type.to_string())
        .fetch_one(&self.pool)
        .await
    }

    async fn find_by_id(&mut self, id: i32) -> Result<Option<TicketHistory>, Error> {
        sqlx::query_as::<_, TicketHistory>(
            r#"SELECT * FROM "TicketHistory" WHERE id = $1"#
        )
        .bind(id)
        .fetch_optional(&self.pool)
        .await
    }

    async fn find_all(&mut self) -> Result<Vec<TicketHistory>, Error> {
        sqlx::query_as::<_, TicketHistory>(
            r#"SELECT * FROM "TicketHistory""#
        )
        .fetch_all(&self.pool)
        .await
    }

    async fn update(&mut self, entity: TicketHistory) -> Result<TicketHistory, Error> {
        sqlx::query_as::<_, TicketHistory>(
            r#"UPDATE "TicketHistory"
               SET  "ticketPubkey" = $1,
                    "userPubkey" = $2,
                    "actionTime" = $3,
                    "actionType" = $4::TEXT::"TicketHistoryActionType"
               WHERE id = $5
               RETURNING *"#
        )
        .bind(entity.ticket_pubkey)
        .bind(entity.user_pubkey)
        .bind(entity.action_time)
        .bind(entity.action_type.to_string())
        .bind(entity.id)
        .fetch_one(&self.pool)
        .await
    }

    async fn delete(&mut self, id: i32) -> Result<(), Error> {
        sqlx::query!(
            r#"DELETE FROM "TicketHistory" WHERE id = $1"#,
            id
        )
        .execute(&self.pool)
        .await
        .map(|_| ())
    }
}
