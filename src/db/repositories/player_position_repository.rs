use chrono::{DateTime, Utc};
use sqlx::{PgPool, Error};
use crate::db::entities::player_position::PlayerPosition;
use super::repository::Repository;

#[derive(Clone)]
pub struct PlayerPositionRepository {
    pub pool: PgPool,
}

impl PlayerPositionRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }
    #[allow(dead_code)]
    pub async fn find_by_user_and_ticket(&mut self, user_pubkey: String, ticket_pubkey: String) -> Result<Option<PlayerPosition>, Error> {
        sqlx::query_as::<_, PlayerPosition>(
            r#"SELECT * FROM "PlayerPosition" WHERE "userPubkey" = $1 AND "ticketPubkey" = $2"#
        )
        .bind(user_pubkey)
        .bind(ticket_pubkey)
        .fetch_optional(&self.pool)
        .await
    }

   pub async fn insert_player_position(
        &mut self,
        user_pubkey: String,
        ticket_pubkey: String,
        chunk_index: i32,
        position_in_chunk: i32,
        chunk_seed: i32,
        update_time: DateTime<Utc>
    ) -> Result<(), Error> {
        sqlx::query!(
            r#"INSERT INTO "PlayerPosition"
               ("userPubkey", "ticketPubkey", "chunkIndex", "chunkSeed", "positionInChunk", "updatedAt")
               VALUES ($1, $2, $3, $4, $5, $6::timestamp)"#,
            user_pubkey,
            ticket_pubkey,
            chunk_index,
            chunk_seed,
            position_in_chunk,
            update_time.naive_utc()
        )
        .execute(&self.pool)
        .await
        .map(|_| ())
    }
    #[allow(dead_code)]
    pub async fn find_by_ticket_and_chunk(
        &mut self,
        ticket_pubkey: String,
        chunk_index: i32
    ) -> Result<Vec<PlayerPosition>, Error> {
        sqlx::query_as::<_, PlayerPosition>(
            r#"SELECT * FROM "PlayerPosition"
               WHERE "ticketPubkey" = $1 AND "chunkIndex" = $2"#
        )
        .bind(ticket_pubkey)
        .bind(chunk_index)
        .fetch_all(&self.pool)
        .await
    }
}

impl Repository<PlayerPosition, i32> for PlayerPositionRepository {

    async fn create(&mut self, entity: PlayerPosition) -> Result<PlayerPosition, Error> {
        sqlx::query_as::<_, PlayerPosition>(
            r#"INSERT INTO "PlayerPosition" (
                "userPubkey", "ticketPubkey", "chunkIndex", "chunkSeed",
                "positionInChunk", "updatedAt"
            )
            VALUES ($1, $2, $3, $4, $5, $6)
            RETURNING *"#
        )
            .bind(entity.user_pubkey)
            .bind(entity.ticket_pubkey)
            .bind(entity.chunk_index)
            .bind(entity.chunk_seed)
            .bind(entity.position_in_chunk)
            .bind(entity.updated_at)
            .fetch_one(&self.pool)
            .await
    }

    async fn find_by_id(&mut self, id: i32) -> Result<Option<PlayerPosition>, Error> {
        sqlx::query_as::<_, PlayerPosition>(
            r#"SELECT * FROM "PlayerPosition" WHERE id = $1"#
        )
        .bind(id)
        .fetch_optional(&self.pool)
        .await
    }

    async fn find_all(&mut self) -> Result<Vec<PlayerPosition>, Error> {
        sqlx::query_as::<_, PlayerPosition>(
            r#"SELECT * FROM "PlayerPosition""#
        )
        .fetch_all(&self.pool)
        .await
    }

    async fn update(&mut self, entity: PlayerPosition) -> Result<PlayerPosition, Error> {
        sqlx::query_as::<_, PlayerPosition>(
            r#"UPDATE "PlayerPosition"
               SET "chunkIndex" = $1, "positionInChunk" = $2, "updatedAt" = $3
               WHERE id = $4
               RETURNING *"#
        )
        .bind(entity.chunk_index)
        .bind(entity.position_in_chunk)
        .bind(entity.updated_at)
        .bind(entity.id)
        .fetch_one(&self.pool)
        .await
    }

    async fn delete(&mut self, id: i32) -> Result<(), Error> {
        sqlx::query!(
            r#"DELETE FROM "PlayerPosition" WHERE id = $1"#,
            id
        )
        .execute(&self.pool)
        .await
        .map(|_| ())
    }
}
