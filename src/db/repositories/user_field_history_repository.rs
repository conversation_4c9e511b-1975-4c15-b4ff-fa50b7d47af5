use crate::db::entities::user_field_history::{NewUserFieldHistory, UserFieldHistory};
use super::repository::Repository;
use sqlx::{PgPool, Error};

#[derive(Clone)]
pub struct UserFieldHistoryRepository {
    pub pool: PgPool,
}

impl UserFieldHistoryRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn create_new(&mut self, entity: NewUserFieldHistory) -> Result<UserFieldHistory, Error> {
        sqlx::query_as::<_, UserFieldHistory>(
            r#"INSERT INTO "UserFieldHistory" ("userPubkey", "ticketPubkey", "fieldValues", "moveObject", "changedAt", "transactionId")
               VALUES ($1, $2, $3, $4, $5, $6)
               RETURNING *"#
        )
        .bind(entity.user_pubkey)
        .bind(entity.ticket_pubkey)
        .bind(entity.field_values)
        .bind(entity.move_object)
        .bind(entity.changed_at)
        .bind(entity.transaction_id)
        .fetch_one(&self.pool)
        .await
    }
}

impl Repository<UserFieldHistory, i32> for UserFieldHistoryRepository {

    async fn create(&mut self, entity: UserFieldHistory) -> Result<UserFieldHistory, Error> {
        sqlx::query_as::<_, UserFieldHistory>(
            r#"INSERT INTO "UserFieldHistory" ("userPubkey", "ticketPubkey", "fieldValues", "moveObject", "changedAt") 
               VALUES ($1, $2, $3, $4, $5)
               RETURNING *"#
        )
        .bind(entity.user_pubkey)
        .bind(entity.ticket_pubkey)
        .bind(entity.field_values)
        .bind(entity.move_object)
        .bind(entity.changed_at)
        .fetch_one(&self.pool)
        .await
    }

    async fn find_by_id(&mut self, id: i32) -> Result<Option<UserFieldHistory>, Error> {
        sqlx::query_as::<_, UserFieldHistory>(
            r#"SELECT * FROM "UserFieldHistory" WHERE id = $1"#
        )
        .bind(id)
        .fetch_optional(&self.pool)
        .await
    }

    async fn find_all(&mut self) -> Result<Vec<UserFieldHistory>, Error> {
        sqlx::query_as::<_, UserFieldHistory>(
            r#"SELECT * FROM "UserFieldHistory""#
        )
        .fetch_all(&self.pool)
        .await
    }

    async fn update(&mut self, entity: UserFieldHistory) -> Result<UserFieldHistory, Error> {
        sqlx::query_as::<_, UserFieldHistory>(
            r#"UPDATE "UserFieldHistory" 
               SET "userPubkey" = $1, "ticketPubkey" = $2, "fieldValues" = $3, "moveObject" = $4, "changedAt" = $5
               WHERE id = $6
               RETURNING *"#
        )
        .bind(entity.user_pubkey)
        .bind(entity.ticket_pubkey)
        .bind(entity.field_values)
        .bind(entity.move_object)
        .bind(entity.changed_at)
        .bind(entity.id)
        .fetch_one(&self.pool)
        .await
    }

    async fn delete(&mut self, id: i32) -> Result<(), Error> {
        sqlx::query!(
            r#"DELETE FROM "UserFieldHistory" WHERE id = $1"#,
            id
        )
        .execute(&self.pool)
        .await
        .map(|_| ())
    }
}
