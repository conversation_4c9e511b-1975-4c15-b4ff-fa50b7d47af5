use crate::db::entities::player_ticket_score_history::{NewPlayerTicketScoreHistory, PlayerTicketScoreHistory};
use sqlx::{PgPool, Error};

#[derive(Clone)]
pub struct PlayerTicketScoreHistoryRepository {
    pool: PgPool,
}

impl PlayerTicketScoreHistoryRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }

    pub async fn create_new(&mut self, entity: NewPlayerTicketScoreHistory) -> Result<PlayerTicketScoreHistory, Error> {
        sqlx::query_as::<_, PlayerTicketScoreHistory>(
            r#"INSERT INTO "PlayerTicketScoreHistory" ("userPubkey", "ticketPubkey", "previousScore", "currentScore", "pointsAdded", "createdAt", "transactionId")
               VALUES ($1, $2, $3, $4, $5, $6, $7)
               RETURNING *"#
        )
        .bind(entity.user_pubkey)
        .bind(entity.ticket_pubkey)
        .bind(entity.previous_score)
        .bind(entity.current_score)
        .bind(entity.points_added)
        .bind(entity.created_at)
        .bind(entity.transaction_id)
        .fetch_one(&self.pool)
        .await
    }
}