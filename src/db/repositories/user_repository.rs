use crate::db::entities::user::User;
use super::repository::Repository;
use sqlx::{PgPool, Error};

#[derive(Clone)]
pub struct UserRepository {
    pub pool: PgPool,
}

impl UserRepository {
    pub fn new(pool: PgPool) -> Self {
        Self { pool }
    }
}

impl Repository<User, String> for UserRepository {

    async fn create(&mut self, user: User) -> Result<User, Error> {
        sqlx::query_as::<_, User>(
            r#"INSERT INTO "User" ("pubkey", "username", "email", "totalEarnings", "withdrawnEarnings",
               "perfectCombinationsWon", "createdAt", "totalEarningsRank", "withdrawnEarningsRank", "perfectCombinationsRank") 
               VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
               RETURNING *"#
        )
        .bind(user.pubkey)
        .bind(user.username)
        .bind(user.email)
        .bind(user.total_earnings)
        .bind(user.withdrawn_earnings)
        .bind(user.perfect_combinations_won)
        .bind(user.created_at)
        .bind(user.total_earnings_rank)
        .bind(user.withdrawn_earnings_rank)
        .bind(user.perfect_combinations_rank)
        .fetch_one(&self.pool)
        .await
    }

    async fn find_by_id(&mut self, pubkey: String) -> Result<Option<User>, Error> {
        sqlx::query_as::<_, User>(
            r#"SELECT * FROM "User" WHERE "pubkey" = $1"#
        )
        .bind(pubkey)
        .fetch_optional(&self.pool)
        .await
    }

    async fn find_all(&mut self) -> Result<Vec<User>, Error> {
        sqlx::query_as::<_, User>(
            r#"SELECT * FROM "User""#
        )
        .fetch_all(&self.pool)
        .await
    }

    async fn update(&mut self, user: User) -> Result<User, Error> {
        sqlx::query_as::<_, User>(
            r#"UPDATE "User" 
               SET "username" = $1, "email" = $2, "totalEarnings" = $3, "withdrawnEarnings" = $4,
                   "perfectCombinationsWon" = $5, "createdAt" = $6, "totalEarningsRank" = $7,
                   "withdrawnEarningsRank" = $8, "perfectCombinationsRank" = $9
               WHERE "pubkey" = $10
               RETURNING *"#
        )
        .bind(user.username)
        .bind(user.email)
        .bind(user.total_earnings)
        .bind(user.withdrawn_earnings)
        .bind(user.perfect_combinations_won)
        .bind(user.created_at)
        .bind(user.total_earnings_rank)
        .bind(user.withdrawn_earnings_rank)
        .bind(user.perfect_combinations_rank)
        .bind(user.pubkey)
        .fetch_one(&self.pool)
        .await
    }

    async fn delete(&mut self, pubkey: String) -> Result<(), Error> {
        sqlx::query!(
            r#"DELETE FROM "User" WHERE "pubkey" = $1"#,
            pubkey
        )
        .execute(&self.pool)
        .await
        .map(|_| ())
    }
}
