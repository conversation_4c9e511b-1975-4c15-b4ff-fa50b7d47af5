use chrono::NaiveDateTime;
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

use crate::db::entities::types::{CurrencyType, DurationType, DurationTimeType, TicketState};

use super::types::TicketCompletedType;

#[derive(Debug, Serialize, Deserialize, FromRow, Clone)]
pub struct Ticket {
    pub uuid: String,
    pub pubkey: String,
    pub currency: CurrencyType,
    #[sqlx(rename = "entryFee")]
    pub entry_fee: f64,
    #[sqlx(rename = "durationType")]
    pub duration_type: DurationType,
    pub duration: Option<DurationTimeType>,
    #[sqlx(rename = "endTime")]
    pub end_time: Option<NaiveDateTime>,
    #[sqlx(rename = "mainBankAmount")]
    pub main_bank_amount: f64,
    #[sqlx(rename = "incomeBankAmount")]
    pub income_bank_amount: f64,
    #[sqlx(rename = "colBankAmount")]
    pub col_bank_amount: f64,
    #[sqlx(rename = "totalPlayers")]
    pub total_players: i32,
    #[sqlx(rename = "createdAt")]
    pub created_at: NaiveDateTime,
    #[sqlx(rename = "currentState")]
    pub current_state: TicketState,
    #[sqlx(rename = "completedType")]
    pub completed_type: Option<TicketCompletedType>,
    #[sqlx(rename = "urlLogo")]
    pub url_logo: String,
    #[sqlx(rename = "urlImage")]
    pub url_image: String,
    #[sqlx(rename = "ticketSeed")]
    pub ticket_seed: i32,
    #[sqlx(rename = "jackpotSeed")]
    pub jackpot_seed: i32,
    #[sqlx(rename = "firstChunkSeed")]
    pub first_chunk_seed: i32,
    #[sqlx(rename = "currentChunkSeed")]
    pub current_chunk_seed: i32,
    #[sqlx(rename = "winnerPubKey")]
    pub winner_pub_key: Option<String>,
    #[sqlx(rename = "creatorPubKey")]
    pub creator_pub_key: Option<String>,
    #[sqlx(rename = "isClaimed")]
    pub is_claimed: bool,
}
