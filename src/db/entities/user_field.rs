use chrono::NaiveDateTime;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use sqlx::FromRow;

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct NewUserField {
    #[sqlx(rename = "userPubkey")]
    pub user_pubkey: String,
    #[sqlx(rename = "ticketPubkey")]
    pub ticket_pubkey: String,
    #[sqlx(rename = "initedField")]
    pub inited_field: Value,
    #[sqlx(rename = "currentField")]
    pub current_field: Value,
    #[sqlx(rename = "updatedAt")]
    pub updated_at: NaiveDateTime,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct UserField {
    pub id: i32,
    #[sqlx(rename = "userPubkey")]
    pub user_pubkey: String,
    #[sqlx(rename = "ticketPubkey")]
    pub ticket_pubkey: String,
    #[sqlx(rename = "initedField")]
    pub inited_field: Value,
    #[sqlx(rename = "currentField")]
    pub current_field: Value,
    #[sqlx(rename = "updatedAt")]
    pub updated_at: NaiveDateTime,
}