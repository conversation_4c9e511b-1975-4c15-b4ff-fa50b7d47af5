use serde::{Deserialize, Serialize};
use chrono::NaiveDateTime;
use sqlx::FromRow;

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct Random {
    pub id: i32,
    #[sqlx(rename = "ticketId")]
    pub ticket_id: String,
    pub hash: String,
    #[sqlx(rename = "isUsed")]
    pub is_used: bool,
    #[sqlx(rename = "userId")]
    pub user_id: String,
    #[sqlx(rename = "txId")]
    pub tx_id: String,
    #[sqlx(rename = "createdAt")]
    pub created_at: NaiveDateTime,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct NewRandom {
    pub ticket_id: String,
    pub hash: String,
    pub user_id: String,
    pub tx_id: String,
} 