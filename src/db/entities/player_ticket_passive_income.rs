use crate::db::entities::types::CurrencyType;
use serde::{Deserialize, Serialize};
use chrono::NaiveDateTime;
use sqlx::FromRow;

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct NewPlayerTicketPassiveIncome {
    #[sqlx(rename = "userPubkey")]
    pub user_pubkey: String,
    #[sqlx(rename = "ticketPubkey")]
    pub ticket_pubkey: String,
    pub currency: CurrencyType,
    pub income: f64,
    #[sqlx(rename = "lastUpdated")]
    pub last_updated: NaiveDateTime,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct PlayerTicketPassiveIncome {
    pub id: i32,
    #[sqlx(rename = "userPubkey")]
    pub user_pubkey: String,
    #[sqlx(rename = "ticketPubkey")]
    pub ticket_pubkey: String,
    pub currency: CurrencyType,
    pub income: f64,
    #[sqlx(rename = "lastUpdated")]
    pub last_updated: NaiveDateTime,
}
