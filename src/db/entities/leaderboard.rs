use chrono::NaiveDateTime;
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

use crate::db::entities::types::CurrencyType;

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct NewTotalEarningsLeaderboard {
    #[sqlx(rename = "userPubkey")]
    pub user_pubkey: String,
    pub earnings: f64,
    pub currency: CurrencyType,
    pub rank: i32,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct TotalEarningsLeaderboard {
    pub id: i32,
    #[sqlx(rename = "userPubkey")]
    pub user_pubkey: String,
    pub earnings: f64,
    pub currency: CurrencyType,
    pub rank: i32,
    #[sqlx(rename = "updatedAt")]
    pub updated_at: NaiveDateTime,
}