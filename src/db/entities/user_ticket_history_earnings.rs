use serde::{Deserialize, Serialize};
use chrono::NaiveDateTime;
use sqlx::FromRow;

use crate::db::entities::types::EarningStatus;

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct NewUserTicketHistoryEarnings {
    #[sqlx(rename = "userPubkey")]
    pub user_pubkey: String,
    #[sqlx(rename = "ticketPubkey")]
    pub ticket_pubkey: String,
    pub amount: f64,
    #[sqlx(rename = "totalAmount")]
    pub total_amount: f64,
    #[sqlx(rename = "fromUserPubkey")]
    pub from_user_pubkey: String,
    pub status: EarningStatus,
    #[sqlx(rename = "createdAt")]
    pub created_at: NaiveDateTime,
    #[sqlx(rename = "transactionId")]
    pub transaction_id: i32,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct UserTicketHistoryEarnings {
    pub id: i32,
    #[sqlx(rename = "userPubkey")]
    pub user_pubkey: String,
    #[sqlx(rename = "ticketPubkey")]
    pub ticket_pubkey: String,
    pub amount: f64,
    #[sqlx(rename = "totalAmount")]
    pub total_amount: f64,
    #[sqlx(rename = "fromUserPubkey")]
    pub from_user_pubkey: String,
    pub status: EarningStatus,
    #[sqlx(rename = "createdAt")]
    pub created_at: NaiveDateTime,
    #[sqlx(rename = "transactionId")]
    pub transaction_id: i32,
}