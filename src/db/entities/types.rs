use serde::{Deserialize, Serialize};
use strum_macros::{Display, EnumString};

// DurationType
#[derive(Debug, Serialize, Deserialize, Clone, Copy, sqlx::Type, EnumString, Display)]
#[sqlx(type_name = "DurationType")]
pub enum DurationType {
    INFINITE,
    TIMED,
}

// DurationTimeType
#[derive(Debug, Serialize, Deserialize, Clone, Copy, sqlx::Type, EnumString, Display)]
#[sqlx(type_name = "DurationTimeType")]
pub enum DurationTimeType {
    MIN_1,
    HOUR_1,
    DAY_1,
    WEEK_1,
    MONTH_1,
    MONTH_3,
    MONTH_6,
    YEAR_1,
    YEAR_3,
    YEAR_5,
}

// CurrencyType
#[derive(Debug, Serialize, Deserialize, Clone, Copy, sqlx::Type, EnumString, Display)]
#[sqlx(type_name = "CurrencyType")]
pub enum CurrencyType {
    SOL,
    USDT,
}

// TicketState
#[derive(Debug, Serialize, Deserialize, Clone, Copy, sqlx::Type, EnumString, Display, PartialEq, Eq)]
#[sqlx(type_name = "TicketState")]
pub enum TicketState {
    INITED,
    ACTIVE,
    COMPLETED,
    CANCELLED,
    CALCULATING,
    ARCHIVED,
    CLEANED,
}

// TransactionType
#[derive(Debug, Serialize, Deserialize, Clone, Copy, sqlx::Type, EnumString, Display)]
#[sqlx(type_name = "TransactionType")]
pub enum TransactionType {
    PARTICIPATION_FEE,
    CHANGE_FIELD,
    JACKPOT_PAYOUT,
}

// TicketHistoryActionType
#[derive(Debug, Serialize, Deserialize, Clone, Copy, sqlx::Type, EnumString, Display)]
#[sqlx(type_name = "TicketHistoryActionType")]
pub enum TicketHistoryActionType {
    ENTRY,
    CHANGE_FIELD,
    COMPLETION,
}

// EarningStatus
#[derive(Debug, Serialize, Deserialize, Clone, Copy, sqlx::Type, EnumString, Display)]
#[sqlx(type_name = "EarningStatus")]
pub enum EarningStatus {
    PENDING,
    PAID,
}

// TicketCompletedType
#[derive(Debug, Serialize, Deserialize, Clone, Copy, sqlx::Type, EnumString, Display)]
#[sqlx(type_name = "TicketCompletedType")]
pub enum TicketCompletedType {
    NONE,
    FIELD,
    EXPIRATION,
}
