use serde::{Deserialize, Serialize};
use chrono::NaiveDateTime;
use sqlx::FromRow;

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct User {
    pub pubkey: String,
    pub username: Option<String>,
    pub email: Option<String>,
    #[sqlx(rename = "totalEarnings")]
    pub total_earnings: f64,
    #[sqlx(rename = "withdrawnEarnings")]
    pub withdrawn_earnings: f64,
    #[sqlx(rename = "perfectCombinationsWon")]
    pub perfect_combinations_won: i32,
    #[sqlx(rename = "createdAt")]
    pub created_at: NaiveDateTime,
    #[sqlx(rename = "totalEarningsRank")]
    pub total_earnings_rank: Option<i32>,
    #[sqlx(rename = "withdrawnEarningsRank")]
    pub withdrawn_earnings_rank: Option<i32>,
    #[sqlx(rename = "perfectCombinationsRank")]
    pub perfect_combinations_rank: Option<i32>,
}