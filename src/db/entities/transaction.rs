use chrono::NaiveDateTime;
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

use crate::db::entities::types::TransactionType;

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct NewTransaction {
    #[sqlx(rename = "userPubkey")]
    pub user_pubkey: String,
    #[sqlx(rename = "ticketPubkey")]
    pub ticket_pubkey: String,
    pub amount: f64,
    #[sqlx(rename = "type")]
    pub transaction_type: TransactionType, // Using raw identifier for "type" keyword
    #[sqlx(rename = "createdAt")]
    pub created_at: NaiveDateTime,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct Transaction {
    pub id: i32,
    #[sqlx(rename = "userPubkey")]
    pub user_pubkey: String,
    #[sqlx(rename = "ticketPubkey")]
    pub ticket_pubkey: String,
    pub amount: f64,
    #[sqlx(rename = "type")]
    pub transaction_type: TransactionType, // Using raw identifier for "type" keyword
    #[sqlx(rename = "createdAt")]
    pub created_at: NaiveDateTime,
}