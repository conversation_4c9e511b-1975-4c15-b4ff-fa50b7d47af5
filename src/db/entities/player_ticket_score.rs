use serde::{Deserialize, Serialize};
use chrono::NaiveDateTime;
use sqlx::FromRow;

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct NewPlayerTicketScore {
    #[sqlx(rename = "ticketPubkey")]
    pub ticket_pubkey: String,
    #[sqlx(rename = "userPubkey")]
    pub user_pubkey: String,
    pub score: i32,
    #[sqlx(rename = "lastUpdated")]
    pub last_updated: NaiveDateTime,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct PlayerTicketScore {
    pub id: i32,
    #[sqlx(rename = "ticketPubkey")]
    pub ticket_pubkey: String,
    #[sqlx(rename = "userPubkey")]
    pub user_pubkey: String,
    pub score: i32,
    #[sqlx(rename = "lastUpdated")]
    pub last_updated: NaiveDateTime,
}
