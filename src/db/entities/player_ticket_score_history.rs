use serde::{Deserialize, Serialize};
use chrono::NaiveDateTime;
use sqlx::FromRow;

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct NewPlayerTicketScoreHistory {
    #[sqlx(rename = "userPubkey")]
    pub user_pubkey: String,
    #[sqlx(rename = "ticketPubkey")]
    pub ticket_pubkey: String,
    #[sqlx(rename = "previousScore")]
    pub previous_score: i32,
    #[sqlx(rename = "currentScore")]
    pub current_score: i32,
    #[sqlx(rename = "pointsAdded")]
    pub points_added: i32,
    #[sqlx(rename = "createdAt")]
    pub created_at: NaiveDateTime,
    #[sqlx(rename = "transactionId")]
    pub transaction_id: i32,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct PlayerTicketScoreHistory {
    pub id: i32,
    #[sqlx(rename = "userPubkey")]
    pub user_pubkey: String,
    #[sqlx(rename = "ticketPubkey")]
    pub ticket_pubkey: String,
    #[sqlx(rename = "previousScore")]
    pub previous_score: i32,
    #[sqlx(rename = "currentScore")]
    pub current_score: i32,
    #[sqlx(rename = "pointsAdded")]
    pub points_added: i32,
    #[sqlx(rename = "createdAt")]
    pub created_at: NaiveDateTime,
    #[sqlx(rename = "transactionId")]
    pub transaction_id: i32,
}
