use chrono::NaiveDateTime;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use sqlx::FromRow;

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct NewUserFieldHistory {
    #[sqlx(rename = "userPubkey")]
    pub user_pubkey: String,
    #[sqlx(rename = "ticketPubkey")]
    pub ticket_pubkey: String,
    #[sqlx(rename = "fieldValues")]
    pub field_values: Value,
    #[sqlx(rename = "moveObject")]
    pub move_object: Value,
    #[sqlx(rename = "changedAt")]
    pub changed_at: NaiveDateTime,
    #[sqlx(rename = "transactionId")]
    pub transaction_id: i32,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct UserFieldHistory {
    pub id: i32,
    #[sqlx(rename = "userPubkey")]
    pub user_pubkey: String,
    #[sqlx(rename = "ticketPubkey")]
    pub ticket_pubkey: String,
    #[sqlx(rename = "fieldValues")]
    pub field_values: Value,
    #[sqlx(rename = "moveObject")]
    pub move_object: Value,
    #[sqlx(rename = "changedAt")]
    pub changed_at: NaiveDateTime,
    #[sqlx(rename = "transactionId")]
    pub transaction_id: i32,
}