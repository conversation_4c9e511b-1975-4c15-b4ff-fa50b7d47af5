use serde::{Deserialize, Serialize};
use chrono::NaiveDateTime;
use sqlx::FromRow;

use crate::db::entities::types::TicketHistoryActionType;

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct NewTicketHistory {
    #[sqlx(rename = "ticketPubkey")]
    pub ticket_pubkey: String,
    #[sqlx(rename = "userPubkey")]
    pub user_pubkey: String,
    #[sqlx(rename = "actionTime")]
    pub action_time: NaiveDateTime,
    #[sqlx(rename = "actionType")]
    pub action_type: TicketHistoryActionType,
    #[sqlx(rename = "transactionId")]
    pub transaction_id: i32,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct TicketHistory {
    pub id: i32,
    #[sqlx(rename = "ticketPubkey")]
    pub ticket_pubkey: String,
    #[sqlx(rename = "userPubkey")]
    pub user_pubkey: String,
    #[sqlx(rename = "actionTime")]
    pub action_time: NaiveDateTime,
    #[sqlx(rename = "actionType")]
    pub action_type: TicketHistoryActionType,
    #[sqlx(rename = "transactionId")]
    pub transaction_id: i32,
}