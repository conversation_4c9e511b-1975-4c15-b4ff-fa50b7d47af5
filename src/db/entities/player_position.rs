use chrono::NaiveDateTime;
use serde::{Deserialize, Serialize};
use sqlx::FromRow;

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct NewPlayerPosition {
    #[sqlx(rename = "userPubkey")]
    pub user_pubkey: String,
    #[sqlx(rename = "ticketPubkey")]
    pub ticket_pubkey: String,
    #[sqlx(rename = "chunkIndex")]
    pub chunk_index: i32,
    #[sqlx(rename = "chunkSeed")]
    pub chunk_seed: i32,
    #[sqlx(rename = "positionInChunk")]
    pub position_in_chunk: i32,
    #[sqlx(rename = "updatedAt")]
    pub updated_at: NaiveDateTime,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct PlayerPosition {
    pub id: i32,
    #[sqlx(rename = "userPubkey")]
    pub user_pubkey: String,
    #[sqlx(rename = "ticketPubkey")]
    pub ticket_pubkey: String,
    #[sqlx(rename = "chunkIndex")]
    pub chunk_index: i32,
    #[sqlx(rename = "chunkSeed")]
    pub chunk_seed: i32,
    #[sqlx(rename = "positionInChunk")]
    pub position_in_chunk: i32,
    #[sqlx(rename = "updatedAt")]
    pub updated_at: NaiveDateTime,
}
