use std::sync::Arc;

use tokio::sync::RwLock;

use crate::{
    services::{
        api_service::ApiService, field_service::FieldService,
        smartcontract_service::SmartContractService,
        ticket_service::TicketService,
        user_service::UserService,
        random_service::RandomService,
    },
    utils::helpers::get_env_var,
    orchestrators::EventOrchestrator
};

use super::repository_container::RepositoryContainer;

#[derive(Clone)]
pub struct AppContext {
    pub api_service: Arc<RwLock<ApiService>>,
    pub ticket_service: Arc<RwLock<TicketService>>,
    pub user_service: Arc<RwLock<UserService>>,
    pub smartcontract_service: Arc<RwLock<SmartContractService>>,
    pub random_service: Arc<RwLock<RandomService>>,
}

impl AppContext {
    pub fn new() -> Self {
        let repository_container = RepositoryContainer::new();
        let api_service = Arc::new(RwLock::new(ApiService::new(get_env_var("NODA_HOST"))));
        let smartcontract_service = Arc::new(RwLock::new(SmartContractService::new()));

        let field_service = Arc::new(RwLock::new(FieldService::new(
            repository_container.clone(),
            smartcontract_service.clone(),
            api_service.clone(),
        )));

        let ticket_service = Arc::new(RwLock::new(TicketService::new(
            repository_container.clone(),
            api_service.clone(),
            smartcontract_service.clone(),
            field_service.clone(),
        )));

        let user_service = Arc::new(RwLock::new(UserService::new(
            repository_container.clone(),
            smartcontract_service.clone(),
            ticket_service.clone(),
        )));

        let random_service = Arc::new(RwLock::new(RandomService::new(
            repository_container.clone(),
        )));

        Self {
            api_service: api_service.clone(),
            smartcontract_service,
            ticket_service,
            user_service,
            random_service
        }
    }
}
