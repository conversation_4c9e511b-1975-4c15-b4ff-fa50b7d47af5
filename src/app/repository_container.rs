use sqlx::postgres::PgPoolOptions;

use crate::{
    db::repositories::{
        player_position_repository::PlayerPositionRepository, 
        player_ticket_passive_income_repository::PlayerTicketPassiveIncomeRepository, 
        player_ticket_score_repository::PlayerTicketScoreRepository, 
        ticket_history_repository::TicketHistoryRepository, 
        ticket_repository::TicketRepository, 
        transaction_repository::TransactionRepository, 
        user_field_history_repository::UserFieldHistoryRepository, 
        user_field_repository::UserFieldRepository, 
        user_repository::UserRepository, 
        user_ticket_history_earnings_repository::UserTicketHistoryEarningsRepository,
        player_ticket_score_history_repository::PlayerTicketScoreHistoryRepository,
        random_repository::RandomRepository
    },
    utils::helpers::get_env_var,
};

#[derive(Clone)]
pub struct RepositoryContainer {
    pub user_field_repository: UserFieldRepository,
    pub ticket_repository: TicketRepository,
    pub ticket_history_repository: TicketHistoryRepository,
    pub transaction_repository: TransactionRepository,
    pub user_repository: UserRepository,
    pub user_field_history_repository: UserFieldHistoryRepository,
    pub player_ticket_score_repository: PlayerTicketScoreRepository,
    pub player_ticket_score_history_repository: PlayerTicketScoreHistoryRepository,
    pub user_ticket_history_earnings_repository: UserTicketHistoryEarningsRepository,
    pub player_ticket_passive_income_repository: PlayerTicketPassiveIncomeRepository,
    pub player_position_repository: PlayerPositionRepository,
    pub random_repository: RandomRepository,
}

impl RepositoryContainer {
    pub fn new() -> Self {
        let database_url = get_env_var("DATABASE_URL");
        let db_pool = PgPoolOptions::new()
            .connect_lazy(&database_url)
            .expect("❌ Failed to create database connection pool");

        Self {
            user_field_repository: UserFieldRepository::new(db_pool.clone()),
            ticket_repository: TicketRepository::new(db_pool.clone()),
            ticket_history_repository: TicketHistoryRepository::new(db_pool.clone()),
            transaction_repository: TransactionRepository::new(db_pool.clone()),
            user_repository: UserRepository::new(db_pool.clone()),
            user_field_history_repository: UserFieldHistoryRepository::new(db_pool.clone()),
            player_ticket_score_repository: PlayerTicketScoreRepository::new(db_pool.clone()),
            player_ticket_score_history_repository: PlayerTicketScoreHistoryRepository::new(db_pool.clone()),
            user_ticket_history_earnings_repository: UserTicketHistoryEarningsRepository::new(db_pool.clone()),
            player_ticket_passive_income_repository: PlayerTicketPassiveIncomeRepository::new(db_pool.clone()),
            player_position_repository: PlayerPositionRepository::new(db_pool.clone()),
            random_repository: RandomRepository::new(db_pool.clone()),
        }
    }
}
