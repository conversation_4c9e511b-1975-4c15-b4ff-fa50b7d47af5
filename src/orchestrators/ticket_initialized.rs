use crate::{
    db::entities::types::{CurrencyType, DurationTimeType, DurationType},
    events::ticket_initialized_event::TicketInitializedEvent,
    errors::orchestrator_error::OrchestratorError
};
use chrono::{Utc, DateTime};
use serde_json::json;
use uuid::Uuid as UuidV4;
use super::EventOrchestrator;

impl EventOrchestrator {
    pub async fn process_ticket_initialization(&mut self, event: TicketInitializedEvent) -> Result<(), OrchestratorError> {
        println!("[DEBUG] ========== Starting process_ticket_initialization in orchestrator ==========");
        println!("[DEBUG] Raw event data: {:?}", event);
        
        let ticket_id_uuid = UuidV4::from_bytes(event.ticket_id);
        let admin_pubkey = event.admin.to_string();
        let creator_pubkey = event.creator.to_string();
        let price = event.price as f64 / 1_000_000_000.0;
        let timestamp = DateTime::from_timestamp(event.timestamp as i64, 0)
            .unwrap_or_else(|| Utc::now());

        println!("[DEBUG] Processed event data:");
        println!("[DEBUG] - Ticket ID (UUID): {}", ticket_id_uuid);
        println!("[DEBUG] - Admin pubkey: {}", admin_pubkey);
        println!("[DEBUG] - Creator pubkey: {}", creator_pubkey);
        println!("[DEBUG] - Price (SOL): {}", price);
        
        // Преобразуем значения из строки/числа в енамы для DB
        let currency = match event.currency.as_str() {
            "SOL" => CurrencyType::SOL,
            "USDT" => CurrencyType::USDT,
            _ => CurrencyType::SOL, // По умолчанию используем SOL
        };

        let duration_type = match event.ticket_end {
            0 => DurationType::INFINITE,
            _ => DurationType::TIMED, // По умолчанию INFINITE
        };

        // Определение duration на основе ticket_end
        // Рассчитываем примерную продолжительность, если ticket_end > 0
        let duration: Option<DurationTimeType> = if event.ticket_end > 0 {
            let current_time = event.timestamp;
            let duration_seconds = event.ticket_end.saturating_sub(current_time);
            
            // Конвертация разницы во времени в DurationTimeType
            match duration_seconds {
                d if d <= 60 => Some(DurationTimeType::MIN_1),
                d if d <= 3600 => Some(DurationTimeType::HOUR_1),
                d if d <= 86400 => Some(DurationTimeType::DAY_1),
                d if d <= 604800 => Some(DurationTimeType::WEEK_1),
                d if d <= 2592000 => Some(DurationTimeType::MONTH_1),
                d if d <= 7776000 => Some(DurationTimeType::MONTH_3),
                d if d <= 15552000 => Some(DurationTimeType::MONTH_6),
                d if d <= 31536000 => Some(DurationTimeType::YEAR_1),
                d if d <= 94608000 => Some(DurationTimeType::YEAR_3),
                d if d > 94608000 => Some(DurationTimeType::YEAR_5),
                _ => None
            }
        } else {
            None
        };

        let end_time = if matches!(duration_type, DurationType::TIMED) && event.ticket_end > 0 {
            Some(DateTime::from_timestamp(event.ticket_end as i64, 0).unwrap_or_else(|| Utc::now()))
        } else {
            None
        };
        
        println!("[DEBUG] - Currency: {:?}", currency);
        println!("[DEBUG] - Duration Type: {:?}", duration_type);
        println!("[DEBUG] - Duration: {:?}", duration);
        println!("[DEBUG] - End Time: {:?}", end_time);
        println!("[DEBUG] - URL Logo: {}", event.url_logo);
        println!("[DEBUG] - URL Image: {}", event.url_image);

        let mut ticket_service = self.ticket_service.write().await;
        
        // Проверяем, существует ли билет уже в базе данных
        println!("[DEBUG] Step 1: Checking if ticket exists in the database");
        println!("[DEBUG] - Ticket ID to search: {}", ticket_id_uuid.to_string());
        
        println!("[DEBUG] Ticket does not exist in the database, creating new record");
                
        // Используем сиды из события
        let ticket_seed = event.ticket_seed as i32;
        let jackpot_seed = event.jackpot_seed as i32;
        let first_chunk_seed = event.first_chunk_seed as i32;
        
        println!("[DEBUG] Using seeds from event: ticket={}, jackpot={}, chunk={}",
                    ticket_seed, jackpot_seed, first_chunk_seed);
        
        let ticket_pda = self.smart_contract_service.read().await.find_ticket_address(&ticket_id_uuid.into_bytes(), ticket_seed as u32);

        // Создаем простой вызов process_ticket_initialization в сервисе с уже преобразованными параметрами
        if let Err(e) = ticket_service.process_ticket_initialization(
            ticket_id_uuid.to_string(),
            ticket_pda.0.to_string(),
            creator_pubkey,
            price,
            currency,
            duration_type,
            duration,
            end_time,
            ticket_seed,
            jackpot_seed,
            first_chunk_seed,
            timestamp,
            event.url_logo.clone(),
            event.url_image.clone()
        ).await {
            eprintln!("❌ [Service] Failed to create ticket: {:?}", e);
            return Err(OrchestratorError::TicketServiceError(e));
        }
        
        println!("[DEBUG] Ticket created successfully through ticket_service");
        
        // Отправляем уведомление на фронтенд
        let notify_body = json!({
            "ticket_id": ticket_id_uuid.to_string(),
        });

        println!("[DEBUG] Sending notification to frontend: {}", notify_body);

        if let Err(e) = self.api_service.write().await.notify_create_ticket(notify_body).await {
            eprintln!("❌ Failed to send ticket initialization notification: {:?}", e);
            // Не возвращаем ошибку, так как это некритичная операция
        }
        
        println!("[DEBUG] ========== Completed process_ticket_initialization in orchestrator successfully ==========");
        Ok(())
    }
} 