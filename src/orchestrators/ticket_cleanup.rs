use crate::{
    db::entities::types::TicketState,
    errors::orchestrator_error::OrchestratorError,
    events::ticket_cleanup_event::TicketCleanupEvent
};

use super::EventOrchestrator;

impl EventOrchestrator {
    pub async fn process_ticket_cleanup(
        &mut self,
        event: TicketCleanupEvent,
    ) -> Result<String, OrchestratorError> {
        self.ticket_service.write().await.update_ticket_status(event.ticket_id, TicketState::CLEANED).await
            .map_err(OrchestratorError::TicketServiceError)?;

        Ok(format!("Ticket status - cleanup"))
    }
}
