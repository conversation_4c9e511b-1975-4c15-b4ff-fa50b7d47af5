use crate::errors::orchestrator_error::OrchestratorError;
use crate::events::TicketChunkCleanupEvent;
use super::EventOrchestrator;
use std::sync::Arc;
use uuid::Uuid;

impl EventOrchestrator {
    pub async fn process_chunk_cleanup(
        &mut self,
        event: TicketChunkCleanupEvent,
    ) -> Result<String, OrchestratorError> {
        let ticket_id_uuid = Uuid::from_bytes(event.ticket_id);

        println!("========== Обработка события очистки чанка ==========");
        println!("- ID билета: {}", ticket_id_uuid);
        println!("- Seed чанка: {}", event.prev_chunk_seed);
        println!("- Индекс чанка: {}", event.chunk_index);

        // Получаем базовую информацию о билете из базы данных
        let ticket = match self
            .ticket_service
            .write()
            .await
            .find_ticket_by_id(event.ticket_id)
            .await
        {
            Ok(ticket) => ticket,
            Err(e) => {
                println!("Ошибка: Не удалось найти билет в базе данных: {}", e);
                return Err(OrchestratorError::TicketServiceError(e));
            }
        };

        // Основная логика очистки:
        // 1. Если индекс чанка == 0, это последний чанк и мы должны очистить архивированный билет
        // 2. Для остальных чанков - стандартная очистка
        if event.chunk_index == 0 {
            println!("Обнаружен чанк с индексом 0 - вызываем очистку архивированного билета");

            // Получаем seed джекпота из базы данных
            let jackpot_seed = ticket.jackpot_seed as u32;

            // Вызываем метод очистки архивированного билета напрямую
            match self
                .smart_contract_service
                .read()
                .await
                .cleanup_archived_ticket(event, jackpot_seed)
                .await
            {
                Ok(signature) => {
                    println!("✅ Успешно очищен архивированный билет: {}", signature);
                    Ok(format!("Архивированный билет очищен: {}", signature))
                }
                Err(e) => {
                    println!("❌ Ошибка при очистке архивированного билета: {}", e);
                    Err(OrchestratorError::OperationFailed(e.to_string()))
                }
            }
        } else {
            // Для всех остальных чанков (индекс > 0)
            println!("Обработка обычного чанка (индекс {})", event.chunk_index);

            // Определяем адрес чанка
            let smart_contract_service = self.smart_contract_service.read().await;
            let chunk_address = smart_contract_service
                .find_chunk_address(&event.ticket_id, event.prev_chunk_seed)
                .0;

            // Проверяем существование чанка
            let client_clone = Arc::clone(&self.smart_contract_service);
            let chunk_address_clone = chunk_address;

            let chunk_exists = match tokio::task::spawn_blocking(move || {
                let service = client_clone.blocking_read();
                service.get_account(&chunk_address_clone)
            })
                .await
            {
                Ok(result) => match result {
                    Ok(account) => account.data.len() > 8,
                    Err(_) => false,
                },
                Err(_) => false,
            };

            if chunk_exists {
                println!("Чанк существует и требует очистки");

                // Отправляем транзакцию очистки чанка
                match smart_contract_service
                    .cleanup_chunk(
                        &event.ticket_id,
                        event.ticket_seed,
                        event.prev_chunk_seed,
                        0, // prev_chunk_seed для первого чанка не используется
                    )
                    .await
                {
                    Ok(signature) => {
                        println!(
                            "✅ Успешно очищен чанк (seed={}), сигнатура: {}",
                            event.prev_chunk_seed, signature
                        );
                        Ok(format!("Чанк очищен: {}", signature))
                    }
                    Err(e) => {
                        println!("❌ Ошибка при очистке чанка: {}", e);
                        Err(OrchestratorError::OperationFailed(e.to_string()))
                    }
                }
            } else {
                println!("Чанк уже очищен или не существует, пропускаем");
                Ok(format!("Чанк уже очищен: {}", chunk_address))
            }
        }
    }
}
