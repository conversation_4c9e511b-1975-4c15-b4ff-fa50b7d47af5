use crate::{
    errors::orchestrator_error::OrchestratorError,
    events::ticket_archived_event::TicketArchivedEvent,
};

use super::EventOrchestrator;

impl EventOrchestrator {
    pub async fn process_ticket_archived(
        &mut self,
        event: TicketArchivedEvent,
    ) -> Result<String, OrchestratorError> {
        self.ticket_service.write().await.process_ticket_archived(event).await
            .map_err(OrchestratorError::TicketServiceError)?;

        Ok("Ticket archived successfully".to_string())
    }
}
