use crate::{
    db::entities::types::TicketCompletedType,
    errors::orchestrator_error::OrchestratorError,
    events::ticket_expired_detected_event::TicketExpiredDetectedEvent,
};

use super::EventOrchestrator;
use uuid::Uuid as UuidV4;

impl EventOrchestrator {
    pub async fn process_ticket_expired(
        &mut self,
        event: TicketExpiredDetectedEvent,
    ) -> Result<String, OrchestratorError> {
        println!("========== Starting process_ticket_expired ==========");

        // 1. Сохраняем данные из события
        let ticket_id = event.ticket_id;
        let timestamp = event.timestamp;
        println!("[INFO] Processing ticket expired event for ticket: {}, timestamp: {}",
                 UuidV4::from_bytes(ticket_id).to_string(), timestamp);

        // 2. Получаем данные о билете и пользователях из TicketService
        let (ticket, max_score) = self.ticket_service.write().await
            .get_ticket_expiration_data(ticket_id).await
            .map_err(OrchestratorError::TicketServiceError)?;

        // 3. Вызываем SmartContractService для обработки события
        println!("[INFO] Calling SmartContractService.complete_ticket");
        let complete_result = self.smart_contract_service.write().await.complete_ticket(
            ticket_id,
            TicketCompletedType::EXPIRATION,
            ticket.ticket_seed as u32
        ).await
        .map_err(|e| {
            println!("[ERROR] Failed to complete ticket in smart contract: {}", e);
            OrchestratorError::SmartContractError(e.to_string())
        })?;

        println!("[INFO] Complete ticket transaction completed: {}", complete_result);

        // 4. Вызываем SmartContractService для генерации случайного числа
        println!("[INFO] Calling SmartContractService.generate_random");
        let random_result = self.smart_contract_service.write().await.generate_random(
            ticket_id,
            max_score,
            ticket.ticket_seed as u32,
            ticket.jackpot_seed as u32
        ).await
        .map_err(|e| {
            println!("[ERROR] Failed to generate random in smart contract: {}", e);
            OrchestratorError::SmartContractError(e.to_string())
        })?;

        println!("[INFO] Random generation transaction completed: {}", random_result);

        // 5. Здесь могла бы быть дополнительная логика, например, уведомление пользователей,
        //    обновление статистики и другие действия

        println!("========== Completed process_ticket_expired successfully ==========");
        Ok("Ticket expired processing completed successfully".to_string())
    }
}
