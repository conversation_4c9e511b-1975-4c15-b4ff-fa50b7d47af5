use crate::{
    errors::orchestrator_error::OrchestratorError,
    events::ticket_finish_event::TicketWinnerEvent,
    utils::helpers::get_env_var
};
use uuid::Uuid as UuidV4;
use super::EventOrchestrator;
use serde_json::json;

impl EventOrchestrator {
    async fn recreate_ticket(&mut self, ticket_id: [u8; 16]) -> Result<(), OrchestratorError> {
        self.ticket_service.write().await
            .recreate_ticket(ticket_id)
            .await
            .map_err(OrchestratorError::TicketServiceError)
    }

    pub async fn process_ticket_winner(
        &mut self,
        event: TicketWinnerEvent,
    ) -> Result<String, OrchestratorError> {
        println!("[DEBUG] ========== Starting on_ticket_winner (DEPRECATED) ==========");
        println!("[DEBUG] Raw event data: {:?}", event);

        let ticket_id_uuid = UuidV4::from_bytes(event.ticket_id);
        println!("[DEBUG] Ticket ID (UUID): {}", ticket_id_uuid);

        println!("[DEBUG] Step 1: Looking up ticket in database");
        let ticket_result = self.ticket_service.write().await.find_ticket_by_id(event.ticket_id).await;

        // Ранний возврат при ошибке
        let ticket = match ticket_result {
            Ok(ticket) => ticket,
            Err(e) => {
                println!("[DEBUG] Ticket not found in database or error occurred: {}", e);
                println!("[DEBUG] ========== Completed on_ticket_finished with error ==========");
                return Ok(format!("Error processing ticket winner: {}", e));
            }
        };

        println!("[DEBUG] Found ticket: {:?}", ticket);
        println!("[DEBUG] creator_pub_key value: {:?}", ticket.creator_pub_key);

        // Проверяем наличие creator_pub_key
        let creator_pub_key = match &ticket.creator_pub_key {
            Some(key) => key,
            None => {
                eprintln!("❌ Ошибка: creator_pub_key отсутствует у билета {}", ticket_id_uuid.to_string());
                println!("[DEBUG] Ticket full details: {:?}", ticket);
                println!("[DEBUG] SQL for checking: SELECT \"pubkey\", \"creatorPubKey\" FROM \"Ticket\" WHERE \"pubkey\" = '{}'",
                         ticket_id_uuid.to_string());
                println!("[DEBUG] ========== Completed on_ticket_finished with missing creator key ==========");
                return Ok(format!("Error: missing creator key for ticket {}", ticket_id_uuid.to_string()));
            }
        };

        println!("[DEBUG] Creator public key exists: {}", creator_pub_key);

        // Проверяем, является ли создатель администратором
        let admin_key = get_env_var("SERVER_ADMIN_PUBLIC_KEY");
        println!("[DEBUG] Admin public key from env: {}", admin_key);

        if creator_pub_key == &admin_key {
            println!("[DEBUG] Ticket was created by admin, calling recreate_ticket");
            if let Err(e) = self.recreate_ticket(event.ticket_id).await {
                eprintln!("❌ Ошибка при пересоздании билета: {:?}", e);
                println!("[DEBUG] ========== Completed on_ticket_finished with recreate error ==========");
                return Ok(format!("Error recreating ticket: {:?}", e));
            }

            let notify_body = json!({
                "ticket_id": ticket_id_uuid.to_string(),
            });

            println!("[DEBUG] Sending notification: {}", notify_body);
            if let Err(e) = self.api_service.write().await.notify_create_ticket(notify_body).await {
                eprintln!("❌ Failed to send ticket initialization notification: {:?}", e);
            }
        } else {
            println!("[DEBUG] Ticket was not created by admin (creator: {}, admin: {}), skipping recreate_ticket",
                    creator_pub_key, admin_key);
        }

        println!("[DEBUG] ========== Completed on_ticket_finished ==========");
        Ok(format!("Processed ticket winner for ticket {}", ticket_id_uuid.to_string()))
    }
}

