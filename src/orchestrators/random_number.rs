use crate::{
    errors::orchestrator_error::OrchestratorError,
    events::RandomResponseEvent,
};
use uuid::Uuid as UuidV4;
use super::EventOrchestrator;
use serde_json::json;
use hex;

impl EventOrchestrator {
    
    pub async fn random_number_handler(
        &mut self,
        event: RandomResponseEvent,
    ) -> Result<String, OrchestratorError> {
        println!("[DEBUG] Raw event data: {:?}", event);

        let ticket_id = UuidV4::from_bytes(event.ticket_id[..16].try_into().unwrap());
        println!("[DEBUG] Ticket ID (UUID): {}", ticket_id);

        let randomness = event.randomness;
        println!("[DEBUG] Randomness: {:?}", randomness);

        let transaction = event.transaction;
        println!("[DEBUG] Transaction: {:?}", transaction);

        let ticket_id_hex = hex::encode(ticket_id);
        let randomness_hex = hex::encode(randomness); // Конвертуємо [u8; 64] в hex-рядок
        let transaction_hex = hex::encode(transaction);
        let payer_hex = hex::encode(event.payer);

        println!("[DEBUG] Ticket ID (hex): {}", ticket_id_hex);
        println!("[DEBUG] Randomness (hex): {}", randomness_hex);
        println!("[DEBUG] Transaction (hex): {}", transaction_hex);
        println!("[DEBUG] Payer (hex): {}", payer_hex);

        println!("[DEBUG] Step 1: Check if user exists");

        // Check if user exists, create if not
        let user_exists = self.user_service.write().await
            .check_user_exists_by_pubkey(&payer_hex)
            .await
            .map_err(|e| OrchestratorError::OperationFailed(format!("Failed to check user: {}", e)))?;

        if !user_exists {
            println!("[DEBUG] User not found, creating new user: {}", payer_hex);
            self.user_service.write().await
                .create_user_if_not_exists(&payer_hex)
                .await
                .map_err(|e| OrchestratorError::OperationFailed(format!("Failed to create user: {}", e)))?;
        }

        println!("[DEBUG] Step 2: Create new random record");
        
        // Create new random record
        let random_record = self.random_service.write().await
            .create_random(
                ticket_id.to_string(),
                randomness_hex.clone(), // Використовуємо hex-рядок randomness як hash
                payer_hex.clone(),
                transaction_hex.clone()
            )
            .await
            .map_err(|e| OrchestratorError::OperationFailed(format!("Failed to create random record: {}", e)))?;

        println!("[DEBUG] Step 3: Call API to notify about random number");
        
        let notification_data = json!({
            "ticket_id": ticket_id_hex,
            "random_id": random_record.id,
            "randomness": randomness_hex,
            "payer": payer_hex,
            "transaction": transaction_hex,
            "created_at": random_record.created_at
        });

        let api_response = self.api_service.write().await
            .notify_random_created(notification_data)
            .await
            .map_err(|e| OrchestratorError::OperationFailed(format!("Failed to notify API: {}", e)))?;

        println!("[DEBUG] API Response status: {}", api_response.status());

        Ok(format!("Successfully processed random number for ticket {}", ticket_id.to_string()))
    }
}

