use crate::{
    errors::orchestrator_error::OrchestratorError,
    events::ticket_purchased_event::TicketPurchasedEvent,
};
use uuid::Uuid as UuidV4;
use super::EventOrchestrator;

impl EventOrchestrator {
    pub async fn process_ticket_purchased(
        &mut self,
        event: TicketPurchasedEvent,
    ) -> Result<(), OrchestratorError> {
        println!("========== Starting process_ticket_purchased in orchestrator ==========");
        let ticket_id_uuid = UuidV4::from_bytes(event.ticket_id);
        let user_id = event.user_id.clone();

          // Получаем базовую информацию о билете из базы данных
        let ticket = match self
          .ticket_service
          .write()
          .await
          .find_ticket_by_id(event.ticket_id)
          .await{
            Ok(ticket) => ticket,
            Err(e) => {
                println!("Ошибка: Не удалось найти билет в базе данных: {}", e);
                return Err(OrchestratorError::TicketServiceError(e));
            }
        };

        let ticket_pda = self.smart_contract_service.read().await.find_ticket_address(&ticket_id_uuid.into_bytes(), ticket.ticket_seed as u32);
        
        println!("[DEBUG] orchestrator:process_ticket_purchased: user={}, ticket={}, field={:?}", 
                 user_id, ticket_id_uuid, event.user_field);
        
        // Делегируем выполнение в ticket_service
        match self.ticket_service.write().await.purchase_ticket(event, ticket_pda).await {
            Ok(_) => {
                println!("========== Completed process_ticket_purchased in orchestrator successfully ==========");
                Ok(())
            },
            Err(e) => {
                println!("========== Failed process_ticket_purchased in orchestrator: {:?} ==========", e);
                Err(OrchestratorError::TicketServiceError(e))
            }
        }
    }
} 