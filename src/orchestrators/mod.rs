pub mod ticket_initialized;
pub mod ticket_archived;
pub mod ticket_cleanup;
pub mod ticket_expired;
pub mod chunk_cleanup;
pub mod ticket_winner;
pub mod ticket_purchased;
pub mod random_number;

use crate::services::{
    smartcontract_service::SmartContractService,
    ticket_service::TicketService,
    api_service::ApiService,
    random_service::RandomService,
    user_service::UserService
};
use tokio::sync::RwLock;
use std::sync::Arc;

pub struct EventOrchestrator {
    smart_contract_service: Arc<RwLock<SmartContractService>>,
    ticket_service: Arc<RwLock<TicketService>>,
    api_service: Arc<RwLock<ApiService>>,
    random_service: Arc<RwLock<RandomService>>,
    user_service: Arc<RwLock<UserService>>,
}

impl EventOrchestrator {
    pub fn new(
        smart_contract_service: Arc<RwLock<SmartContractService>>,
        ticket_service: Arc<RwLock<TicketService>>,
        api_service: Arc<RwLock<ApiService>>,
        random_service: Arc<RwLock<RandomService>>,
        user_service: Arc<RwLock<UserService>>,
    ) -> Self {
        Self {
            smart_contract_service,
            ticket_service,
            api_service,
            random_service,
            user_service,
        }
    }
}
