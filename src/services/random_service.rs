use std::sync::Arc;
use tokio::sync::RwLock;

use crate::{
    app::repository_container::RepositoryContainer,
    db::{
        entities::random::{Random, NewRandom},
        repositories::{
            random_repository::RandomRepository,
            repository::Repository,
        },
    },
    errors::ticket_service_error::TicketServiceError,
};

pub struct RandomService {
    random_repository: RandomRepository,
}

impl RandomService {
    pub fn new(repository_container: RepositoryContainer) -> Self {
        Self {
            random_repository: repository_container.random_repository,
        }
    }

    /// Створює новий запис Random
    pub async fn create_random(&mut self, ticket_id: String, hash: String, user_id: String, tx_id: String) -> Result<Random, TicketServiceError> {
        let new_random = NewRandom {
            ticket_id,
            hash,
            user_id,
            tx_id,
        };

        self.random_repository.create_new(new_random)
            .await
            .map_err(|e| TicketServiceError::DatabaseError(e))
    }

    /// Знаходить всі записи Random для конкретного билета
    pub async fn find_by_ticket_id(&mut self, ticket_id: String) -> Result<Vec<Random>, TicketServiceError> {
        self.random_repository.find_by_ticket_id(ticket_id)
            .await
            .map_err(|e| TicketServiceError::DatabaseError(e))
    }

    /// Знаходить всі записи Random для конкретного користувача
    pub async fn find_by_user_id(&mut self, user_id: String) -> Result<Vec<Random>, TicketServiceError> {
        self.random_repository.find_by_user_id(user_id)
            .await
            .map_err(|e| TicketServiceError::DatabaseError(e))
    }

    /// Знаходить запис Random за хешем
    pub async fn find_by_hash(&mut self, hash: String) -> Result<Option<Random>, TicketServiceError> {
        self.random_repository.find_by_hash(hash)
            .await
            .map_err(|e| TicketServiceError::DatabaseError(e))
    }

    /// Знаходить невикористані записи Random для конкретного билета
    pub async fn find_unused_by_ticket_id(&mut self, ticket_id: String) -> Result<Vec<Random>, TicketServiceError> {
        self.random_repository.find_unused_by_ticket_id(ticket_id)
            .await
            .map_err(|e| TicketServiceError::DatabaseError(e))
    }

    /// Позначає запис Random як використаний
    pub async fn mark_as_used(&mut self, id: i32) -> Result<Random, TicketServiceError> {
        self.random_repository.mark_as_used(id)
            .await
            .map_err(|e| TicketServiceError::DatabaseError(e))
    }

    /// Знаходить записи Random для конкретного билета та користувача
    pub async fn find_by_ticket_and_user(&mut self, ticket_id: String, user_id: String) -> Result<Vec<Random>, TicketServiceError> {
        self.random_repository.find_by_ticket_and_user(ticket_id, user_id)
            .await
            .map_err(|e| TicketServiceError::DatabaseError(e))
    }

    /// Отримує запис Random за ID
    pub async fn find_by_id(&mut self, id: i32) -> Result<Option<Random>, TicketServiceError> {
        self.random_repository.find_by_id(id)
            .await
            .map_err(|e| TicketServiceError::DatabaseError(e))
    }

    /// Оновлює запис Random
    pub async fn update(&mut self, random: Random) -> Result<Random, TicketServiceError> {
        self.random_repository.update(random)
            .await
            .map_err(|e| TicketServiceError::DatabaseError(e))
    }

    /// Видаляє запис Random
    pub async fn delete(&mut self, id: i32) -> Result<(), TicketServiceError> {
        self.random_repository.delete(id)
            .await
            .map_err(|e| TicketServiceError::DatabaseError(e))
    }

    /// Перевіряє, чи існує запис з таким хешем
    pub async fn hash_exists(&mut self, hash: String) -> Result<bool, TicketServiceError> {
        let result = self.random_repository.find_by_hash(hash).await
            .map_err(|e| TicketServiceError::DatabaseError(e))?;
        
        Ok(result.is_some())
    }

    /// Отримує кількість невикористаних записів для билета
    pub async fn get_unused_count_for_ticket(&mut self, ticket_id: String) -> Result<usize, TicketServiceError> {
        let unused_records = self.random_repository.find_unused_by_ticket_id(ticket_id).await
            .map_err(|e| TicketServiceError::DatabaseError(e))?;
        
        Ok(unused_records.len())
    }
} 