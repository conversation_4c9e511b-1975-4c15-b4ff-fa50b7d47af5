use std::sync::Arc;

use chrono::{DateTime, Utc};
use solana_sdk::pubkey::Pubkey;
use uuid::Uuid as UuidV4;
use tokio::sync::RwLock;
use serde_json::json;

use crate::{
    app::repository_container::RepositoryContainer,
    db::{
        entities::{
            player_ticket_passive_income::*, player_ticket_score::*, ticket::Ticket, ticket_history::*, transaction::*, types::*, user::User, user_field::*, user_field_history::*, user_ticket_history_earnings::*, player_ticket_score_history::*,
        },
        repositories::{
            player_ticket_score_history_repository::PlayerTicketScoreHistoryRepository, player_position_repository::PlayerPositionRepository, player_ticket_passive_income_repository::PlayerTicketPassiveIncomeRepository, player_ticket_score_repository::PlayerTicketScoreRepository, repository::Repository, ticket_history_repository::TicketHistoryRepository, ticket_repository::TicketRepository, transaction_repository::TransactionRepository, user_field_history_repository::UserFieldHistoryRepository, user_field_repository::UserFieldRepository, user_repository::UserRepository, user_ticket_history_earnings_repository::UserTicketHistoryEarningsRepository
        },
    },
    errors::ticket_service_error::TicketServiceError,
    events::{
        jackpot_claim_event::JackpotClaimEvent, ticket_purchased_event::TicketPurchasedEvent, ticket_withdraw_passive_income::PassiveIncomeWithdrawEvent, ticket_archived_event::TicketArchivedEvent,
    }, utils::helpers::{future_date_depends, generate_multiple_seeds},

};


use super::field_service::FieldService;
use super::{api_service::ApiService, smartcontract_service::SmartContractService};

pub struct TicketService {
    user_repository: UserRepository,
    ticket_repository: TicketRepository,
    api_service: Arc<RwLock<ApiService>>,
    field_service: Arc<RwLock<FieldService>>,
    user_field_repository: UserFieldRepository,
    transaction_repository: TransactionRepository,
    ticket_history_repository: TicketHistoryRepository,
    smartcontract_service: Arc<RwLock<SmartContractService>>,
    user_field_history_repository: UserFieldHistoryRepository,
    player_ticket_score_repository: PlayerTicketScoreRepository,
    player_ticket_score_history_repository: PlayerTicketScoreHistoryRepository,
    user_ticket_history_earnings_repository: UserTicketHistoryEarningsRepository,
    player_ticket_passive_income_repository: PlayerTicketPassiveIncomeRepository,
    player_position_repository: PlayerPositionRepository
}

impl TicketService {
    pub fn new(repository_container: RepositoryContainer,
               api_service: Arc<RwLock<ApiService>>,
               smartcontract_service: Arc<RwLock<SmartContractService>>,
               field_service: Arc<RwLock<FieldService>>) -> Self {
        Self {
            ticket_repository: repository_container.ticket_repository,
            api_service,
            transaction_repository: repository_container.transaction_repository,
            ticket_history_repository: repository_container.ticket_history_repository,
            user_repository: repository_container.user_repository,
            user_field_repository: repository_container.user_field_repository,
            user_field_history_repository: repository_container.user_field_history_repository,
            player_ticket_score_repository: repository_container.player_ticket_score_repository,
            player_ticket_score_history_repository: repository_container.player_ticket_score_history_repository,
            user_ticket_history_earnings_repository: repository_container.user_ticket_history_earnings_repository,
            player_ticket_passive_income_repository: repository_container.player_ticket_passive_income_repository,
            smartcontract_service: smartcontract_service,
            player_position_repository: repository_container.player_position_repository,
            field_service
        }
    }

    pub async fn process_ticket_initialization(
        &mut self,
        ticket_id: String,
        ticket_pda: String,
        creator_pubkey: String,
        price: f64,
        currency: CurrencyType, 
        duration_type: DurationType, 
        duration: Option<DurationTimeType>,
        end_time: Option<DateTime<Utc>>,
        ticket_seed: i32,
        jackpot_seed: i32,
        first_chunk_seed: i32,
        timestamp: DateTime<Utc>,
        url_logo: String,
        url_image: String
    ) -> Result<(), TicketServiceError> {
        println!("[DEBUG] ========== Starting process_ticket_initialization in TicketService ==========");

        println!("[DEBUG] Creating new ticket:");
        println!("[DEBUG] - Ticket ID: {}", ticket_id);
        println!("[DEBUG] - Creator pubkey: {}", creator_pubkey);
        println!("[DEBUG] - Seeds: ticket={}, jackpot={}, chunk={}", 
                 ticket_seed, jackpot_seed, first_chunk_seed);

        // Создаем новую запись в базе данных с метаданными
        let new_ticket = Ticket {
            uuid: ticket_id.clone(),
            pubkey: ticket_pda,
            currency,
            entry_fee: price,
            duration_type,
            duration,
            end_time: end_time.map(|t| t.naive_utc()),
            main_bank_amount: 0.0,
            income_bank_amount: 0.0,
            col_bank_amount: 0.0,
            total_players: 0,
            created_at: timestamp.naive_utc(),
            current_state: TicketState::ACTIVE,
            completed_type: None,
            url_logo,
            url_image,
            ticket_seed,
            jackpot_seed,
            first_chunk_seed,
            current_chunk_seed: first_chunk_seed,
            winner_pub_key: None,
            creator_pub_key: Some(creator_pubkey),
            is_claimed: false,
        };

        println!("[DEBUG] New ticket object before saving: {:?}", new_ticket);

        match self.ticket_repository.create(new_ticket).await {
            Ok(created_ticket) => {
                println!("[DEBUG] Ticket created successfully: {:?}", created_ticket);
                println!("[DEBUG] Creator pubkey saved: {:?}", created_ticket.creator_pub_key);
                Ok(())
            },
            Err(e) => {
                eprintln!("❌ [DB] Failed to create ticket: {:?}", e);
                Err(TicketServiceError::DatabaseError(e))
            }
        }
    }

    pub async fn purchase_ticket(&mut self, event: TicketPurchasedEvent, ticket_pda: (Pubkey, u8)) -> Result<(), TicketServiceError> {
        let ticket_id_uuid = UuidV4::from_bytes(event.ticket_id);
        let user_id = event.user_id.clone();

        println!("[DEBUG] ticket_service:purchase_ticket:get EVENT: user={}, ticket={}, field={:?}", 
                 user_id, ticket_id_uuid, event.user_field);
        println!("[DEBUG] ticket_service:purchase_ticket:ROUND_INFO: index={}, direction={}, diff={}", 
                 event.round_index, event.round_dir, event.round_diff);

        // Сначала проверяем, существует ли пользователь
        let user_exists = self.user_repository.find_by_id(user_id.to_string()).await?;
        // Если пользователя нет, создаем его
        if user_exists.is_none() {
            println!("[DEBUG] ticket_service:purchase_ticket:creating_new_user: {}", user_id);


            // Создаем базовое имя пользователя
            let user_id_str = user_id.to_string();
            let username = if user_id_str.len() >= 8 {
                format!("User-{}", &user_id_str[..8])
            } else {
                format!("User-{}", user_id_str)
            };

            // Создаем базового пользователя
            let new_user = User {
                pubkey: user_id.to_string(),
                username: Some(username),
                email: None,
                total_earnings: 0.0,
                withdrawn_earnings: 0.0,
                perfect_combinations_won: 0,
                created_at: Utc::now().naive_utc(),
                total_earnings_rank: None,
                withdrawn_earnings_rank: None,
                perfect_combinations_rank: None,
            };

            match self.user_repository.create(new_user).await {
                Ok(_) => println!("[DEBUG] ticket_service:purchase_ticket:user_created_successfully"),
                Err(e) => {
                    eprintln!("❌ [DB] Failed to create user: {:?}", e);
                    return Err(TicketServiceError::DatabaseError(e));
                }
            }
        }

        let jackpot_amount = event.jackpot_amount.clone();
        let participant_index = event.participant_index.clone();
        let chunk_index = event.chunk_index.clone();

        println!("[DEBUG] ticket_service:purchase_ticket:ticket_id_uuid: {}", ticket_id_uuid);
        println!("[DEBUG] ticket_service:purchase_ticket:event.user_id: {:?}", user_id);

        let update_time = Utc::now();

        let transaction = self.ticket_repository.pool.begin().await.map_err(TicketServiceError::DatabaseError)?;

        match self.player_position_repository
            .insert_player_position(
                user_id.to_string(),
                ticket_pda.0.to_string(),
                chunk_index as i32,
                participant_index as i32,
                event.chunk_seed as i32,
                update_time
            )
            .await {
            Ok(_) => {
                // println!("[DEBUG] Player position saved successfully")
            },
            Err(e) => {
                eprintln!("❌ [DB] Failed to insert player position: {:?}", e);
                return Err(TicketServiceError::InvalidData(e.to_string()));
            }
        }

        // Создаем запись о транзакции в базе данных (покупка билета)
        println!("[DEBUG] ticket_service:purchase_ticket:transaction_repository");
        let new_transaction =  self.transaction_repository.create_new(NewTransaction {
            user_pubkey: user_id.to_string(),
            ticket_pubkey: ticket_pda.0.to_string(),
            amount: event.amount as f64 / 1_000_000_000.0,
            transaction_type: TransactionType::PARTICIPATION_FEE,
            created_at: update_time.naive_utc(),
        }).await?;

        // Создаем запись в истории билета о том что билет был куплен
        println!("[DEBUG] ticket_service:purchase_ticket:ticket_history_repository.create_new");
        self.ticket_history_repository.create_new(NewTicketHistory {
            ticket_pubkey: ticket_pda.0.to_string(),
            user_pubkey: user_id.to_string(),
            action_type: TicketHistoryActionType::ENTRY,
            action_time: update_time.naive_utc(),
            transaction_id: new_transaction.id,
        }).await?;

        let user_field = "[".to_string() + &event.user_field.iter().map(|&x| x.to_string()).collect::<Vec<String>>().join(",") + "]";

        // Создаем запись о полях пользователя которые прислал смартконтракт
        println!("[DEBUG] ticket_service:purchase_ticket:user_field_repository.create_new");
        self.user_field_repository.create_new(NewUserField {
            user_pubkey: user_id.to_string(),
            ticket_pubkey: ticket_pda.0.to_string(),
            inited_field: serde_json::from_str(&user_field).unwrap(),
            current_field: serde_json::from_str(&user_field).unwrap(),
            updated_at: update_time.naive_utc(),
        }).await?;

        // Создаем запись в истории полей пользователя
        println!("[DEBUG] ticket_service:purchase_ticket:user_field_history_repository.create_new");
        self.user_field_history_repository.create_new(NewUserFieldHistory {
            user_pubkey: user_id.to_string(),
            ticket_pubkey: ticket_pda.0.to_string(),
            field_values: serde_json::from_str(&user_field).unwrap(),
            move_object: json!({
                "index": event.round_index,
                "dir": event.round_dir,
                "diff": event.round_diff,
            }),
            changed_at: update_time.naive_utc(),
            transaction_id: new_transaction.id,
        }).await?;

        // Рассчитываем счет игрока
        let score = event.user_field.iter().enumerate().filter(|&(i, &v)| i as u8 == v).count();

        // Создаем запись о счете игрока
        println!("[DEBUG] ticket_service:purchase_ticket:player_ticket_score_repository.create_new");
        let new_player_ticket_score = self.player_ticket_score_repository.create_new(NewPlayerTicketScore {
            ticket_pubkey: ticket_pda.0.to_string(),
            user_pubkey: user_id.to_string(),
            score: score as i32,
            last_updated: update_time.naive_utc(),
        }).await?;

        // Создаем запись о счете игрока
        println!("[DEBUG] ticket_service:purchase_ticket:player_ticket_score_repository.create_new");
        self.player_ticket_score_history_repository.create_new(NewPlayerTicketScoreHistory {
            user_pubkey: new_player_ticket_score.user_pubkey,
            ticket_pubkey: new_player_ticket_score.ticket_pubkey,
            previous_score: 0,
            current_score: new_player_ticket_score.score,
            points_added: score as i32,
            created_at: update_time.naive_utc(),
            transaction_id: new_transaction.id,
        }).await?;

        // Recalculate fields for all users in the ticket. Define the winner if any and send the request to the smart contract
        println!("[DEBUG] ticket_service:purchase_ticket:field_service.fields_handle)");
        println!("!!!! TICKET_SERVICE: ПЕРЕД ВЫЗОВОМ fields_handler");
        println!("!!!! TICKET_SERVICE: ПАРАМЕТРЫ: ticket_id={:?}, user={}, field={:?}",
                 event.ticket_id, event.user_id, event.user_field);
        
        let fields_handler_result = self.field_service.write().await.fields_handler(event.clone(), update_time, ticket_pda.0.to_string(), new_transaction.id).await;
        
        println!("!!!! TICKET_SERVICE: РЕЗУЛЬТАТ fields_handler: {:?}", fields_handler_result.is_ok());
        
        if let Err(e) = fields_handler_result {
            println!("!!!! TICKET_SERVICE: ОШИБКА В fields_handler: {:?}", e);
            return Err(e);
        }

        transaction.commit().await.map_err(TicketServiceError::DatabaseError)?;
        println!("[DEBUG] ticket_service:purchase_ticket:db_transaction:success");

        let ticket_entity = self.ticket_repository.find_by_id(ticket_id_uuid.to_string()).await?;
        if ticket_entity.is_none() {
            return Err(TicketServiceError::NotFound);
        }

        // Обновляем количество игроков и банк билета
        let mut ticket = ticket_entity.unwrap();
        ticket.total_players += 1;
        ticket.main_bank_amount = jackpot_amount as f64;

        if let Err(e) = self.ticket_repository.update(ticket).await {
            eprintln!("❌ [DB] Failed to update ticket: {:?}", e);
            return Err(TicketServiceError::DatabaseError(e));
        }
        println!("[DEBUG] ticket_service:purchase_ticket:ticket updated ");

        let body = serde_json::json!({
            "user_id": user_id.to_string(),
            "ticket_id": ticket_id_uuid.to_string(),
        });
        println!("[DEBUG] ticket_service:purchase_ticket:body: {}", body);

        if let Err(e) = self.api_service.write().await.purchase_ticket(body).await {
            eprintln!("❌ Failed to purchase ticket: {:?}", e);
        }

        // Получаем данные о билете для определения цены
        println!("[DEBUG] ticket_service:purchase_ticket:ticket_repository.find_by_id");
        let ticket_entity = self.ticket_repository.find_by_id(ticket_id_uuid.to_string()).await?;
        if let Some(ticket) = ticket_entity {
            // Распределяем пассивный доход
            println!("[DEBUG] ticket_service:purchase_ticket:distribute_passive_income");
            self.distribute_passive_income(
                event.ticket_id,
                user_id.to_string(),
                ticket.entry_fee, // Используем цену билета из БД
                update_time,
                new_transaction.id
            ).await?;
        }

        println!("[DEBUG] ticket_service:purchase_ticket:updated_all_player_scores");
        self.update_all_player_scores(user_id.to_string(), ticket_pda.0.to_string(), new_transaction.id).await?;

        println!("[DEBUG] ticket_service:purchase_ticket:success");

        // нужно залезтиь в длок чейн собрать все чайны  с них взять все раунды, и под каждого юзика подставить и посмотреть есть ли у кого джекпот

        Ok(())
    }

    // Функция для распределения пассивного дохода в бэкенде
    pub async fn distribute_passive_income(
        &mut self,
        ticket_id: [u8; 16],
        buyer_pubkey: String,
        ticket_price: f64,
        timestamp: DateTime<Utc>,
        transaction_id: i32
    ) -> Result<(), TicketServiceError> {
        // Константы для модели распределения
        const MIRROR_PERCENTAGE: f64 = 12.0;
        const EQUAL_PERCENTAGE: f64 = 33.0;
        const MAX_RECENT_PLAYERS: usize = 30;
        const TOTAL_PERCENTAGE: f64 = MIRROR_PERCENTAGE + EQUAL_PERCENTAGE;

        let ticket_id_uuid = UuidV4::from_bytes(ticket_id);
        println!("[DEBUG] ticket_service:distribute_passive_income:ticket_id: {}", ticket_id_uuid);

        let ticket_pubkey;
        // Обновляем билет в базе данных
        println!("[DEBUG] ticket_service:distribute_passive_income:ticket_repository.find_by_id");
        if let Some(ticket) = self.ticket_repository.find_by_id(ticket_id_uuid.to_string()).await? {
            ticket_pubkey = ticket.pubkey;
        } else {
            return Err(TicketServiceError::NotFound);
        }

        // Получаем всех предыдущих участников билета
        println!("[DEBUG] ticket_service:distribute_passive_income:user_field_repository.find_all_by_ticket_id");
        let user_fields = self.user_field_repository.find_all_by_ticket_id(ticket_pubkey.clone()).await?;

        // Если нет предыдущих участников или только один текущий покупатель, пассивного дохода нет
        if user_fields.is_empty() || (user_fields.len() == 1 && user_fields[0].user_pubkey == buyer_pubkey) {
            return Ok(());
        }

        // Получаем предыдущих участников, исключая текущего покупателя
        let previous_participants: Vec<_> = user_fields.iter()
            .filter(|user_field| user_field.user_pubkey != buyer_pubkey)
            .collect();

        let total_participants = previous_participants.len();

        // Обновляем билет в базе данных
        println!("[DEBUG] ticket_service:distribute_passive_income:ticket_repository.find_by_id");
        if let Some(mut ticket) = self.ticket_repository.find_by_id(ticket_id_uuid.to_string()).await? {
            // 45% от цены билета идет на пассивный доход
            let passive_income_total = ticket_price * 0.45;
            ticket.income_bank_amount += passive_income_total;
            self.ticket_repository.update(ticket).await?;
        } else {
            return Err(TicketServiceError::NotFound);
        }

        // Общий пассивный доход от этого билета для распределения
        let passive_income_total = ticket_price * 0.45;

        // Распределяем равную часть (33%)
        let total_equal_income = passive_income_total * (EQUAL_PERCENTAGE / TOTAL_PERCENTAGE);
        let equal_income_per_participant = total_equal_income / (total_participants as f64);

        // Распределяем зеркальную часть (12%)
        let total_mirror_income = passive_income_total * (MIRROR_PERCENTAGE / TOTAL_PERCENTAGE);

        // Определяем, сколько игроков участвуют в зеркальном распределении
        let players_for_mirror = std::cmp::min(total_participants, MAX_RECENT_PLAYERS);

        // Рассчитываем сумму гармонического ряда
        let mut harmonic_sum = 0.0;
        for i in 1..=players_for_mirror {
            harmonic_sum += 1.0 / (i as f64);
        }

        // Сортируем игроков по времени их входа (от новых к старым)
        let mut sorted_participants = previous_participants.clone();
        sorted_participants.sort_by(|a, b| b.updated_at.cmp(&a.updated_at));

        // Берем только первых players_for_mirror игроков для зеркальной части
        let mirror_participants = if sorted_participants.len() > players_for_mirror {
            sorted_participants[0..players_for_mirror].to_vec()
        } else {
            sorted_participants
        };

        // Распределяем пассивный доход для каждого игрока
        let mut distributions = Vec::new();

        // Сначала всем даем равную часть
        for participant in previous_participants.iter() {
            distributions.push((participant.user_pubkey.clone(), equal_income_per_participant));
        }

        // Затем распределяем зеркальную часть среди выбранных игроков
        println!("[DEBUG] ticket_service:distribute_passive_income:mirror_participants");
        for (index, participant) in mirror_participants.iter().enumerate() {
            // В зеркальной модели новые участники получают больше
            let position = index + 1; // Позиция начинается с 1
            let weight = 1.0 / (position as f64);
            let normalized_weight = weight / harmonic_sum; // Нормализуем вес 0.66, 0.33, 0.22 и т.д.
            let mirror_share = total_mirror_income * normalized_weight;

            // Находим этого участника в общем списке и добавляем ему зеркальную часть
            for (pubkey, amount) in &mut distributions {
                if pubkey == &participant.user_pubkey {
                    *amount += mirror_share;
                    break;
                }
            }
        }

        // Обрабатываем каждого участника
        println!("[DEBUG] ticket_service:distribute_passive_income:process_each_participant");
        for (user_pubkey, amount) in distributions {
            // Обновляем или создаем запись в PlayerTicketPassiveIncome
            let existing_income = self.player_ticket_passive_income_repository
                .get_income(user_pubkey.clone(), ticket_pubkey.clone())
                .await?;

            match existing_income {
                Some(_) => {
                    // Если запись существует, увеличиваем сумму
                    self.player_ticket_passive_income_repository
                        .update_income(user_pubkey.clone(), ticket_pubkey.clone(), amount)
                        .await?;
                },
                None => {
                    // Если записи нет, создаем новую
                    let ticket_entity = self.ticket_repository.find_by_id(ticket_id_uuid.to_string()).await?;
                    if let Some(ticket) = ticket_entity {
                        self.player_ticket_passive_income_repository
                            .create_new(NewPlayerTicketPassiveIncome {
                                user_pubkey: user_pubkey.clone(),
                                ticket_pubkey: ticket_pubkey.clone(),
                                currency: ticket.currency,
                                income: amount,
                                last_updated: timestamp.naive_utc(),
                            })
                            .await?;
                    }
                }
            }

            // Создаем запись в истории доходов
            println!("[DEBUG] ticket_service:distribute_passive_income:user_ticket_history_earnings_repository.get_total_amount_by_user_and_ticket");
            let existing_earnings = self.user_ticket_history_earnings_repository
                .get_total_amount_by_user_and_ticket(user_pubkey.clone(), ticket_pubkey.clone())
                .await
                .unwrap_or(0.0);

            // Создаем запись в истории доходов
            println!("[DEBUG] ticket_service:distribute_passive_income:user_ticket_history_earnings_repository.create_new");
            self.user_ticket_history_earnings_repository.create_new(
                NewUserTicketHistoryEarnings {
                    user_pubkey: user_pubkey.clone(),
                    ticket_pubkey: ticket_pubkey.clone(),
                    amount,
                    total_amount: existing_earnings + amount,
                    from_user_pubkey: buyer_pubkey.clone(),
                    status: EarningStatus::PENDING,
                    created_at: timestamp.naive_utc(),
                    transaction_id
                }
            ).await?;
        }

        Ok(())
    }

    pub async fn update_all_player_scores(&mut self, current_buyer_pubkey: String, ticket_pubkey: String, transaction_id: i32) -> Result<(), TicketServiceError> {

        // Получаем все поля пользователей для данного билета
        let user_fields = self.user_field_repository.find_all_by_ticket_id(ticket_pubkey.clone()).await?;

        for user_field in user_fields {
            // Пропускаем пользователя, который только что купил билет
            if user_field.user_pubkey == current_buyer_pubkey {
                continue;
            }

            // Преобразуем JSON в массив u8
            if let Ok(current_field) = serde_json::from_value::<Vec<u8>>(user_field.current_field.clone()) {
                // Находим существующую запись по user_pubkey и ticket_pubkey
                let query_result = self.player_ticket_score_repository
                    .find_by_user_and_ticket(user_field.user_pubkey.clone(), ticket_pubkey.clone())
                    .await;

                if let Ok(player_score)  = query_result {
                    // Рассчитываем счет (сколько чисел на своих местах)
                    let new_matched_count = current_field.iter().enumerate()
                        .filter(|&(i, &v)| i as u8 == v)
                        .count() as i32;

                    if player_score.is_some() {
                        let player_score = player_score.unwrap();
                        let new_score = player_score.score + new_matched_count;
                        let now = Utc::now();

                        self.player_ticket_score_repository
                            .update_player_score(new_score, now.clone(), player_score.id)
                            .await?;

                        // Создаем запись о счете игрока
                        println!("[DEBUG] ticket_service:purchase_ticket:player_ticket_score_repository.create_new");
                        self.player_ticket_score_history_repository.create_new(NewPlayerTicketScoreHistory {
                            user_pubkey: player_score.user_pubkey,
                            ticket_pubkey: player_score.ticket_pubkey,
                            previous_score: player_score.score,
                            current_score: new_score,
                            points_added: new_matched_count,
                            created_at: now.naive_utc(),
                            transaction_id,
                        }).await?;
                    }
                }
            }
        }

        Ok(())
    }

    pub async fn process_passive_income_withdrawal(&mut self, event: PassiveIncomeWithdrawEvent) -> Result<(), TicketServiceError> {
        let ticket_id_uuid = UuidV4::from_bytes(event.ticket_id);
        let amount = event.amount;

        println!("[DEBUG] ticket_service:process_passive_income_withdrawal:ticket_id: {}, amount: {}", ticket_id_uuid, amount);

        // Получаем билет из базы данных
        let ticket_entity = self.ticket_repository.find_by_id(ticket_id_uuid.to_string()).await?;

        if ticket_entity.is_none() {
            return Err(TicketServiceError::NotFound);
        }

        // Обновляем значение incomeBankAmount
        let mut ticket = ticket_entity.unwrap();
        let ticket_pubkey = ticket.pubkey.clone();
        // Проверка на достаточность средств (для безопасности)
        if ticket.income_bank_amount < (amount as f64 / 1_000_000_000.0) {
            eprintln!("❌ [WARNING] Ticket doesn't have enough income_bank_amount ({} < {})",
                      ticket.income_bank_amount, amount as f64 / 1_000_000_000.0);
            // Корректируем значение, чтобы не уйти в отрицательное
            ticket.income_bank_amount = 0.0;
        } else {
            // Вычитаем значение
            ticket.income_bank_amount -= amount as f64 / 1_000_000_000.0;
        }

        // Обновляем билет в базе данных
        match self.ticket_repository.update(ticket).await {
            Ok(updated_ticket) => {
                println!("[DEBUG] ticket_service:process_passive_income_withdrawal:updated_income_bank_amount: {}",
                         updated_ticket.income_bank_amount);
            },
            Err(e) => {
                eprintln!("❌ [DB] Failed to update ticket income_bank_amount: {:?}", e);
                return Err(TicketServiceError::DatabaseError(e));
            }
        }

        let user_pubkey = event.user.to_string();

        // После обновления билета в базе данных добавить следующий код:
        // Обновляем статус записей пассивного дохода на PAID
        let update_status_result = self.user_ticket_history_earnings_repository
            .update_earnings_status_to_paid(ticket_id_uuid.to_string(), user_pubkey.clone())
            .await;

        match update_status_result {
            Ok(result) => {
                println!("[DEBUG] ticket_service:process_passive_income_withdrawal:earnings_status_updated_to_paid: {} rows", result);
            },
            Err(e) => {
                eprintln!("❌ [DB] Failed to update earnings status: {:?}", e);
                // Не возвращаем ошибку, чтобы не прерывать основную транзакцию
            }
        }

        // Обнуляем пассивный доход пользователя в PlayerTicketPassiveIncome
        match self.player_ticket_passive_income_repository
            .reset_income(user_pubkey.clone(), ticket_pubkey.clone())
            .await
        {
            Ok(_) => {
                println!("[DEBUG] ticket_service:process_passive_income_withdrawal:passive_income_reset_to_zero");
            },
            Err(e) => {
                eprintln!("❌ [DB] Failed to reset passive income: {:?}", e);
                // Не возвращаем ошибку, чтобы не прерывать основную транзакцию
            }
        }

        let user_pubkey_clone = user_pubkey.clone();

        // Обнуляем очки пользователя
        let update_result = self.player_ticket_score_repository
            .reset_player_score(ticket_pubkey.clone(), user_pubkey, Utc::now())
            .await;

        match update_result {
            Ok(result) => {
                if result > 0 {
                    println!("[DEBUG] ticket_service:process_passive_income_withdrawal:player_score_reset_to_zero");
                } else {
                    eprintln!("❌ [WARNING] Player score record not found for user: {}, ticket: {}",
                              user_pubkey_clone, ticket_id_uuid);
                }
            },
            Err(e) => {
                eprintln!("❌ [DB] Failed to reset player score: {:?}", e);
            }
        }

        // Оповещаем фронтенд о выводе пассивного дохода
        let notify_body = json!({
            "ticket_id": ticket_id_uuid.to_string(),
            "user_id": user_pubkey_clone
        });

        println!("[DEBUG] ticket_service:process_passive_income_withdrawal:notify_body: {}", notify_body);

        // Вызываем API для оповещения фронтенда
        if let Err(e) = self.api_service.write().await.notify_passive_income_withdrawal(notify_body).await {
            eprintln!("❌ Failed to notify frontend about passive income withdrawal: {:?}", e);
        }

        println!("[DEBUG] ticket_service:process_passive_income_withdrawal:success");

        Ok(())
    }

    pub async fn process_jackpot_claim(&mut self, event: JackpotClaimEvent) -> Result<(), TicketServiceError> {
        println!("[DEBUG] ========== Starting process_jackpot_claim ==========");
        println!("[DEBUG] Raw event data: {:?}", event);

        let ticket_id_uuid = UuidV4::from_bytes(event.ticket_id);
        let winner_pubkey = event.winner.to_string();
        let amount = event.amount as f64 / 1_000_000_000.0;

        // Получаем билет из базы данных
        let ticket_entity = self.ticket_repository.find_by_id(ticket_id_uuid.to_string()).await?;

        if ticket_entity.is_none() {
            return Err(TicketServiceError::NotFound);
        }

        // Обновляем значение incomeBankAmount
        let ticket = ticket_entity.unwrap();
        let ticket_pubkey = ticket.pubkey.clone();

        println!("[DEBUG] Processed event data:");
        println!("[DEBUG] - Ticket ID (UUID): {}", ticket_id_uuid);
        println!("[DEBUG] - Winner pubkey: {}", winner_pubkey);
        println!("[DEBUG] - Amount (SOL): {}", amount);

        // 1. Проверка существующего завершения
        println!("[DEBUG] Step 1: Checking for existing completion");
        match self.ticket_history_repository
            .find_completion(ticket_pubkey.clone(), winner_pubkey.clone())
            .await
        {
            Ok(Some(completion)) => {
                println!("[DEBUG] Found existing completion record:");
                println!("[DEBUG] - Action time: {:?}", completion.action_time);
                return Ok(());
            }
            Ok(None) => println!("[DEBUG] No existing completion record found"),
            Err(e) => {
                println!("[ERROR] Failed to check completion: {:?}", e);
                return Err(TicketServiceError::DatabaseError(e));
            }
        }

        // 2. Обновление статуса билета
        println!("[DEBUG] Step 2: Updating ticket status");
        match self.ticket_repository.find_by_id(ticket_id_uuid.to_string()).await? {
            Some(mut ticket) => {
                println!("[DEBUG] Found ticket, current state: {:?}", ticket.current_state);
                ticket.current_state = TicketState::COMPLETED;
                ticket.end_time = Some(Utc::now().naive_utc());
                ticket.is_claimed = true;
                match self.ticket_repository.update(ticket).await {
                    Ok(_) => println!("[DEBUG] Successfully updated ticket status"),
                    Err(e) => {
                        println!("[ERROR] Failed to update ticket: {:?}", e);
                        return Err(TicketServiceError::DatabaseError(e));
                    }
                }
            }
            None => {
                println!("[ERROR] Ticket not found: {}", ticket_id_uuid);
                return Err(TicketServiceError::NotFound);
            }
        }

        // 3. Обновление данных пользователя
        println!("[DEBUG] Step 3: Updating user data");
        match self.user_repository.find_by_id(winner_pubkey.clone()).await? {
            Some(mut user) => {
                println!("[DEBUG] Found user, current stats:");
                println!("[DEBUG] - Total earnings: {}", user.total_earnings);
                println!("[DEBUG] - Withdrawn earnings: {}", user.withdrawn_earnings);
                println!("[DEBUG] - Perfect combinations won: {}", user.perfect_combinations_won);

                user.total_earnings += amount;
                user.withdrawn_earnings += amount;
                user.perfect_combinations_won += 1;

                match self.user_repository.update(user).await {
                    Ok(_) => println!("[DEBUG] Successfully updated user data"),
                    Err(e) => {
                        println!("[ERROR] Failed to update user: {:?}", e);
                        return Err(TicketServiceError::DatabaseError(e));
                    }
                }
            }
            None => println!("[WARN] User not found, skipping user update"),
        }

        // 4. Создание записи о транзакции
        println!("[DEBUG] Step 4: Creating transaction record");
        let now = Utc::now();
        match self.transaction_repository.create_new(NewTransaction {
            user_pubkey: winner_pubkey.clone(),
            ticket_pubkey: ticket_pubkey.clone(),
            amount,
            transaction_type: TransactionType::JACKPOT_PAYOUT,
            created_at: now.naive_utc(),
        }).await {
            Ok(_) => println!("[DEBUG] Successfully created transaction record"),
            Err(e) => {
                println!("[ERROR] Failed to create transaction: {:?}", e);
                return Err(TicketServiceError::DatabaseError(e));
            }
        }

        // 5. Обновление истории билета
        println!("[DEBUG] Step 5: Creating ticket completion record");
        match self.ticket_history_repository.create_ticket_completion(
            ticket_pubkey.clone(),
            winner_pubkey.clone(),
            now
        ).await {
            Ok(_) => println!("[DEBUG] Successfully created ticket completion record"),
            Err(e) => {
                println!("[ERROR] Failed to create ticket history: {:?}", e);
                return Err(TicketServiceError::DatabaseError(e));
            }
        }

        // Оповещаем фронтенд о получении джекпота
        let notify_body = json!({
            "ticket_id": ticket_id_uuid.to_string(),
            "winner_id": winner_pubkey.clone()
        });

        println!("[DEBUG] ticket_service:notify_jackpot_claim:notify_body: {}", notify_body);

        // Вызываем API для оповещения фронтенда
        if let Err(e) = self.api_service.write().await.notify_jackpot_claim(notify_body).await {
            eprintln!("❌ Failed to notify frontend about jackpot claim: {:?}", e);
        }

        println!("[DEBUG] ========== process_jackpot_claim completed successfully ==========");
        Ok(())
    }

    pub async fn find_ticket_by_id(&mut self, ticket_id_uuid: [u8; 16]) -> Result<Ticket, TicketServiceError> {
        let ticket_id_string = UuidV4::from_bytes(ticket_id_uuid).to_string();
        println!("[DEBUG] Finding ticket in database by ID: {:?}", ticket_id_uuid);
        match self.ticket_repository.find_by_id(ticket_id_string.clone()).await? {
            Some(ticket) => {
                println!("[DEBUG] Found ticket with seeds: ticket_seed={}, jackpot_seed={}",
                         ticket.ticket_seed, ticket.jackpot_seed);
                Ok(ticket)
            }
            None => {
                println!("[WARNING] Ticket not found in database: {:?}", &ticket_id_string);
                Err(TicketServiceError::NotFound)
            }
        }
    }

    pub async fn recreate_ticket(&mut self, ticket_id: [u8; 16]) -> Result<(), TicketServiceError> {
        let ticket_id_string = UuidV4::from_bytes(ticket_id).to_string();
        println!("[DEBUG] ========== Starting recreate_ticket ==========");
        println!("[DEBUG] Ticket ID to recreate: {}", ticket_id_string);

        println!("[DEBUG] Step 1: Looking up original ticket in database");
        let ticket = self.ticket_repository.find_by_id(ticket_id_string.clone()).await?;

        if let Some(ticket) = ticket {
            println!("[DEBUG] Found original ticket: {:?}", ticket);
            println!("[DEBUG] Original creator_pub_key: {:?}", ticket.creator_pub_key);

            let timestamp = Utc::now();
            let seeds = generate_multiple_seeds(3);
            let ticket_seed = seeds[0];
            let jackpot_seed = seeds[1];
            let first_chunk_seed = seeds[2];

            println!("[DEBUG] Generated new seeds: ticket={}, jackpot={}, chunk={}",
                     ticket_seed, jackpot_seed, first_chunk_seed);

            // Проверка наличия creator_pub_key
            if let Some(creator_pub_key) = &ticket.creator_pub_key {
                println!("[DEBUG] Creator public key exists: {}", creator_pub_key);
                // Create a new ticket with the same parameter from previous ticket
                let new_ticket = Ticket {
                    uuid: UuidV4::new_v4().to_string(),
                    pubkey: ticket.pubkey,
                    currency: ticket.currency,
                    entry_fee: ticket.entry_fee,
                    duration_type: ticket.duration_type,
                    duration: ticket.duration,
                    end_time: ticket.duration.map(|d| {
                        future_date_depends(timestamp, d).naive_utc()
                    }),
                    main_bank_amount: 0.0,
                    income_bank_amount: 0.0,
                    col_bank_amount: 0.0,
                    total_players: 0,
                    created_at: timestamp.naive_utc(),
                    current_state: TicketState::INITED,
                    completed_type: None,
                    url_logo: ticket.url_logo,
                    url_image: ticket.url_image,
                    ticket_seed: ticket_seed as i32,
                    jackpot_seed: jackpot_seed as i32,
                    first_chunk_seed: first_chunk_seed as i32,
                    current_chunk_seed: first_chunk_seed as i32,
                    winner_pub_key: None,
                    creator_pub_key: ticket.creator_pub_key.clone(),
                    is_claimed: false,
                };

                println!("[DEBUG] New ticket object to be created: {:?}", new_ticket);
                println!("[DEBUG] New ticket creator_pub_key: {:?}", new_ticket.creator_pub_key);

                match self.ticket_repository.create(new_ticket).await {
                    Ok(created_ticket) => {
                        println!("[DEBUG] Successfully created new ticket: {:?}", created_ticket.pubkey);
                        println!("[DEBUG] New ticket creator_pub_key: {:?}", created_ticket.creator_pub_key);

                        let ticket_uuid = UuidV4::parse_str(&created_ticket.pubkey).expect("Failed to parse UUID");
                        let ticket_id_bytes = ticket_uuid.as_bytes();

                        println!("[DEBUG] Step 2: Initializing smart contract");

                        // // Установка времени окончания билета
                        let current_time = Utc::now().timestamp();
                        println!("[DEBUG] Ticket end time calculation:");
                        println!("[DEBUG] - Current time: {}", current_time);
                        println!("[DEBUG] - Original end_time: {:?}", created_ticket.end_time);

                        // Call smart contract initialization
                        if let Err(e) = self.smartcontract_service.write().await
                            .initialize_ticket(
                                ticket_id_bytes.as_ref().try_into().unwrap(),
                                (created_ticket.entry_fee * 1_000_000_000.0) as u64,
                                created_ticket.ticket_seed as u32,
                                created_ticket.jackpot_seed as u32,
                                created_ticket.first_chunk_seed as u32,
                                created_ticket.end_time.unwrap_or_else(|| Utc::now().naive_utc()).and_utc().timestamp() as u64,
                                false
                            ).await
                        {
                            eprintln!("❌ Failed to initialize ticket in smart contract: {:?}", e);
                            return Err(TicketServiceError::SmartContractError(e.to_string()));
                        }

                        println!("[DEBUG] Smart contract initialized successfully");

                        let notify_body = json!({
                                "ticket_id": ticket_id_string,
                            });

                        println!("[DEBUG] Sending notification: {}", notify_body);
                        match self.api_service.write().await.notify_create_ticket(notify_body).await {
                            Ok(_) => (), // Игнорируем успешный ответ
                            Err(e) => {
                                eprintln!("❌ Failed to send ticket initialization notification: {:?}", e);
                            }
                        }
                    },
                    Err(e) => {
                        eprintln!("❌ [DB] Failed to create ticket: {:?}", e);
                        return Err(TicketServiceError::DatabaseError(e));
                    }
                }
            } else {
                eprintln!("❌ Ошибка: creator_pub_key отсутствует у билета {}", ticket_id_string);
                println!("[DEBUG] Ticket missing creator_pub_key: {:?}", ticket);
                return Err(TicketServiceError::InvalidTicketData(format!("Ticket {} has no creator_pub_key", ticket_id_string)));
            }
        } else {
            println!("[DEBUG] Original ticket not found: {}", ticket_id_string);
            return Err(TicketServiceError::NotFound);
        }

        println!("[DEBUG] ========== Completed recreate_ticket ==========");
        Ok(())
    }

    pub async fn process_ticket_archived(&mut self, event: TicketArchivedEvent) -> Result<(), TicketServiceError> {
        let ticket_id_uuid = UuidV4::from_bytes(event.ticket_id);
        println!("[DEBUG] Processing ticket archived event for ticket: {}", ticket_id_uuid);

        // Обновление статуса билета
        println!("[DEBUG] Step 1: Updating ticket status");
        match self.ticket_repository.find_by_id(ticket_id_uuid.to_string()).await? {
            Some(mut ticket) => {
                println!("[DEBUG] Found ticket, current state: {:?}", ticket.current_state);
                ticket.current_state = TicketState::ARCHIVED;

                match self.ticket_repository.update(ticket).await {
                    Ok(_) => println!("[DEBUG] Successfully updated ticket status to ARCHIVED"),
                    Err(e) => {
                        println!("[ERROR] Failed to update ticket: {:?}", e);
                        return Err(TicketServiceError::DatabaseError(e));
                    }
                }
            }
            None => {
                println!("[ERROR] Ticket not found: {}", ticket_id_uuid);
                return Err(TicketServiceError::NotFound);
            }
        }

        Ok(())
    }

    pub async fn update_ticket_status(&mut self, ticket_id: [u8; 16], status: TicketState) -> Result<(), TicketServiceError> {
        let ticket_id_uuid = UuidV4::from_bytes(ticket_id);

        match self.ticket_repository.find_by_id(ticket_id_uuid.to_string()).await? {
            Some(mut ticket) => {
                println!("[DEBUG] Found ticket, current state: {:?}", ticket.current_state);

                // Обновляем статус напрямую enum-ом
                ticket.current_state = status;

                // Сохраняем обновленный тикет
                match self.ticket_repository.update(ticket).await {
                    Ok(_) => {
                        println!("[DEBUG] Successfully updated ticket status to {:?}", status);
                        Ok(())
                    },
                    Err(e) => {
                        println!("[ERROR] Failed to update ticket: {:?}", e);
                        Err(TicketServiceError::DatabaseError(e))
                    }
                }
            }
            None => {
                println!("[ERROR] Ticket not found: {}", ticket_id_uuid);
                Err(TicketServiceError::NotFound)
            }
        }
    }

    // Метод для получения данных о билете и пользователях при истечении срока действия
    pub async fn get_ticket_expiration_data(&mut self, ticket_id: [u8; 16]) -> Result<(Ticket, i32), TicketServiceError> {
        let ticket_id_string = UuidV4::from_bytes(ticket_id).to_string();
        println!("[DEBUG] Getting ticket expiration data for ticket ID: {}", &ticket_id_string);

        // Получаем билет из базы данных
        let ticket_entity = self.ticket_repository.find_by_id(ticket_id_string.clone()).await?;

        if ticket_entity.is_none() {
            return Err(TicketServiceError::NotFound);
        }

        // Обновляем значение incomeBankAmount
        let ticket = ticket_entity.unwrap();
        let ticket_pubkey = ticket.pubkey.clone();

        // Получаем список всех пользователей, участвующих в билете
        let users = self.player_ticket_score_repository.find_all_users_by_ticket(ticket_pubkey.to_string().as_str()).await?;
        println!("[DEBUG] Found {} users participating in ticket", users.len());

        // Находим максимальный счет
        let max_score = users.iter().map(|u| u.score).max().unwrap_or(0);
        println!("[DEBUG] Maximum score among users: {}", max_score);

        // Получаем информацию о билете
        let ticket_entity = self.ticket_repository.find_by_id(ticket_id_string.clone()).await?;

        match ticket_entity {
            Some(ticket) => {
                println!("[DEBUG] Found ticket with seeds: ticket_seed={}, jackpot_seed={}",
                         ticket.ticket_seed, ticket.jackpot_seed);
                Ok((ticket, max_score))
            }
            None => {
                println!("[WARNING] Ticket not found in database: {}", &ticket_id_string);
                Err(TicketServiceError::NotFound)
            }
        }
    }
}
