use std::collections::HashMap;
use std::any::TypeId;
use std::sync::Arc;

use solana_client::rpc_config::{RpcTransactionLogsConfig, RpcTransactionLogsFilter};
use solana_client::nonblocking::pubsub_client::PubsubClient;
use solana_sdk::commitment_config::CommitmentConfig;
use base64::prelude::BASE64_STANDARD;
use borsh::BorshDeserialize;
use futures_util::StreamExt;
use tokio::sync::RwLock;
use base64::Engine;

use crate::events::RandomResponseEvent;
use crate::{
    TicketArchivedEvent,
    TicketChunkCleanupEvent,
    TicketCleanupEvent,
    JackpotClaimEvent,
    JackpotWeightedRandomValueEvent,
    TicketCompleteEvent,
    TicketWinnerEvent,
    TicketInitializedEvent,
    TicketPurchasedEvent,
    PassiveIncomeWithdrawEvent,
    TicketExpiredDetectedEvent
};

pub struct PubSubService {
    ws_blockchain: String,
    client: Option<PubsubClient>,
    pub_key: String,
    event_listeners: Arc<RwLock<HashMap<TypeId, Vec<Box<dyn Fn(Vec<u8>) + Send + Sync>>>>>,
}

impl PubSubService {
    pub fn new(ws_blockchain: &str, pub_key: &str) -> Self {
        Self {
            ws_blockchain: ws_blockchain.to_string(),
            client: None,
            pub_key: pub_key.to_string(),
            event_listeners: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    pub async fn connect(&mut self) -> Result<&mut Self, Box<solana_client::pubsub_client::PubsubClientError>>{
        let client = PubsubClient::new(&self.ws_blockchain).await;

        match client {
            Ok(client) => {
                println!("Connected to the Blockchain PubSub server");
                self.client = Some(client);

                Ok(self)
            },
            Err(e) => {
                println!("Failed to connect to the Blockchain PubSub server: {:?}", e);
                Err(Box::new(e))
            },
        }
    }

    pub async fn subscribe<T, F>(&mut self, callback: F) -> &mut Self
    where
        T: BorshDeserialize + Send + Sync + 'static,
        F: Fn(T) + Send + Sync + 'static,
    {
        println!("!!!! DEBUG: Subscribing to event type: {}", std::any::type_name::<T>());

        let event_listeners = Arc::clone(&self.event_listeners);
        let mut listeners = event_listeners.write().await;
        let type_id = TypeId::of::<T>();

        listeners.entry(type_id)
            .or_insert_with(Vec::new)
            .push(Box::new(move |data| {
                println!("!!!! DEBUG: Raw data length: {} bytes", data.len());
                println!("!!!! DEBUG: Raw data (hex): {:?}", data.iter()
                    .map(|b| format!("{:02x}", b))
                    .collect::<Vec<String>>()
                    .join(" "));

                match T::try_from_slice(&data) {
                    Ok(event) => {
                        println!("!!!! DEBUG: Successfully deserialized event");
                        callback(event);
                    }
                    Err(e) => {
                        println!("!!!! DEBUG: Deserialization error: {:?}", e);
                        println!("!!!! DEBUG: Expected type: {}", std::any::type_name::<T>());
                    }
                }
            }));

        self
    }

    pub async fn start_listen(&self) -> Result<(), Box<dyn std::error::Error>> {
        let filter = RpcTransactionLogsFilter::Mentions(vec![self.pub_key.to_string()]);
        let config = RpcTransactionLogsConfig { commitment: Some(CommitmentConfig::confirmed()) };
        let client = self.client.as_ref().unwrap();
        let (mut logs, _unsubscribe) = client.logs_subscribe(filter, config).await?;

        while let Some(log) = logs.next().await {
            for line in log.value.logs {
                if line.starts_with("Program log: Program event") {
                    let parts: Vec<&str> = line.split(":").collect();
                    if parts.len() < 3 {
                        continue;
                    }
                    let event_name = parts[2].trim();
                    let encoded_data = parts[3].trim().replace("\"", "");

                    println!("[DEBUG] Received event: {}", event_name);
                    println!("[DEBUG] Encoded data: {}", encoded_data);

                    if let Ok(decoded) = BASE64_STANDARD.decode(&encoded_data) {
                        println!("[DEBUG] Successfully decoded event data");
                        let listeners = self.event_listeners.read().await;
                        let type_id = Self::event_type_id(event_name);

                        if let Some(callbacks) = listeners.get(&type_id) {
                            println!("[DEBUG] Found {} listeners for event {}", callbacks.len(), event_name);
                            for callback in callbacks {
                                callback(decoded.clone());
                            }
                        } else {
                            println!("[DEBUG] No listeners found for event {}", event_name);
                        }
                    } else {
                        println!("[DEBUG] Failed to decode event data");
                    }
                }
            }
        }
        Ok(())
    }

    fn event_type_id(event_name: &str) -> TypeId {
        match event_name {
            "TicketInitializedEvent" => TypeId::of::<TicketInitializedEvent>(),
            "TicketPurchasedEvent" => TypeId::of::<TicketPurchasedEvent>(),
            "PassiveIncomeWithdrawEvent" => TypeId::of::<PassiveIncomeWithdrawEvent>(),
            "JackpotClaimEvent" => TypeId::of::<JackpotClaimEvent>(),
            "TicketCompleteEvent" => TypeId::of::<TicketCompleteEvent>(),
            "TicketExpiredDetectedEvent" => TypeId::of::<TicketExpiredDetectedEvent>(),
            "JackpotWeightedRandomValueEvent" => TypeId::of::<JackpotWeightedRandomValueEvent>(),
            "TicketWinnerEvent" => TypeId::of::<TicketWinnerEvent>(),
            "TicketArchivedEvent" => TypeId::of::<TicketArchivedEvent>(),
            "TicketChunkCleanupEvent" => TypeId::of::<TicketChunkCleanupEvent>(),
            "TicketCleanupEvent" => TypeId::of::<TicketCleanupEvent>(),
            "RandomResponseEvent" => TypeId::of::<RandomResponseEvent>(),
            _ => TypeId::of::<()>(),
        }
    }
}
