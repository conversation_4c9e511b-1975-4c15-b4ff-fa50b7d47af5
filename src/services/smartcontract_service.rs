use crate::{
    db::entities::types::TicketCompletedType, errors::ticket_service_error::TicketServiceError,
    events::ticket_chunk_cleanup_event::TicketChunkCleanupEvent, utils::helpers::get_env_var,
};

use log::{error, info, warn};
use std::{io, str::FromStr, sync::Arc};

use solana_client::client_error::ClientError;
use solana_client::rpc_client::RpcClient;
use solana_client::rpc_config::RpcSendTransactionConfig;
use solana_sdk::account::Account;
use solana_sdk::commitment_config::{CommitmentConfig, CommitmentLevel};
use solana_sdk::instruction::{AccountMeta, Instruction};
use solana_sdk::message::Message;
use solana_sdk::pubkey::Pubkey;
use solana_sdk::signature::{Keypair, Signer};
use solana_sdk::transaction::Transaction;

use borsh::{BorshDeserialize, BorshSerialize};

pub struct SmartContractService {
    rpc_client: Arc<RpcClient>,
    program_id: Pubkey,
    admin_keypair: Keypair,
}

#[derive(BorshDeserialize, BorshSerialize)]
pub struct TicketAccount {
    pub is_active: bool,
    pub admin: Pubkey,
    pub ticket_id: [u8; 16],
    pub price: u64,
    pub total_participants: u64,
    pub is_locked_withdraw: bool,
    pub is_locked_buy: bool,
    pub is_locked: bool,
    pub is_completed: bool,
    pub ticket_end: u64,
    pub current_chunk_index: u64,
    pub current_chunk_seed: u32,
    pub chunks_count: u32,
    pub cleaned_chunks_count: u32,
    pub creation_seed: u32,
    pub chunk_seed_base: [u8; 32],
    pub ticket_completed_by: u8,
    pub is_test_generate_field: bool,
}

#[derive(BorshSerialize, BorshDeserialize)]
pub struct ParticipantsChunk {
    pub version: u8, // Добавлено: версия структуры аккаунта
    pub admin: Pubkey,
    pub ticket_id: [u8; 16],
    pub chunk_index: u64,
    pub chunk_seed: u32,
    pub current_count: u16,
    pub prev_chunk: Option<Pubkey>,
    pub prev_chunk_seed: Option<u32>, // Добавлено: сид предыдущего чанка
    pub next_chunk_seed: Option<u32>, // Добавлено: сид следующего чанка
    pub participants: Vec<Player>,
    pub rounds_history: Vec<Round>,
}

#[derive(BorshSerialize, BorshDeserialize)]
pub struct Player {
    pub version: u8,
    pub pubkey: Pubkey,
    pub field: [u8; 9],
    pub total_withdrawn: u64,
}

#[derive(BorshSerialize, BorshDeserialize)]
pub struct Round {
    pub version: u8,    // Добавлено: версия структуры
    pub timestamp: u64, // Clock::get()?.unix_timestamp
    pub index: u8,      // 0-8
    pub dir: u8,        // 0-3
    pub diff: u8,       // 0-1
}

impl SmartContractService {
    pub fn new() -> Self {
        let rpc_url = get_env_var("SOLANA_RPC_URL");
        let client = RpcClient::new(rpc_url);

        let admin_keypair_bytes = get_env_var("ADMIN_KEYPAIR");
        let admin_keypair = Keypair::from_bytes(
            &admin_keypair_bytes
                .split(',')
                .map(|s| s.trim().parse::<u8>().expect("Wrong keypair format"))
                .collect::<Vec<u8>>(),
        )
            .expect("Unnable to parse admin keypair");

        let program_id =
            Pubkey::from_str(&get_env_var("PROGRAM_ID")).expect("Wrong program ID format");

        Self {
            rpc_client: Arc::new(client),
            program_id,
            admin_keypair,
        }
    }

    pub async fn initialize_ticket(
        &self,
        ticket_id: [u8; 16],
        price: u64,
        ticket_seed: u32,
        jackpot_seed: u32,
        first_chunk_seed: u32,
        ticket_end: u64,
        is_test_generate_field: bool,
    ) -> Result<String, TicketServiceError> {
        // Проверка, что ticket_end больше текущего времени
        let current_time = chrono::Utc::now().timestamp() as u64;
        if ticket_end <= current_time {
            println!(
                "[ERROR] ticket_end ({}) должен быть больше текущего времени ({})",
                ticket_end, current_time
            );
            return Err(TicketServiceError::InvalidData(format!(
                "Недействительное время окончания билета: {} должно быть больше текущего времени: {}",
                ticket_end,
                current_time
            )));
        }

        // Проверка минимального значения ticket_end (хотя бы на 1 минуту больше текущего времени)
        let min_ticket_end = current_time + 60;
        if ticket_end < min_ticket_end {
            println!("[ERROR] ticket_end ({}) должен быть как минимум на 1 минуту больше текущего времени ({})", ticket_end, min_ticket_end);
            return Err(TicketServiceError::InvalidData(format!(
                "Недействительное время окончания билета: {} должно быть как минимум: {}",
                ticket_end, min_ticket_end
            )));
        }

        let (ticket_address, _) = self.find_ticket_address(&ticket_id, ticket_seed);
        let (jackpot_address, _) = self.find_jackpot_address(&ticket_id, jackpot_seed);
        let (chunk_address, _) = self.find_chunk_address(&ticket_id, first_chunk_seed);

        println!("[DEBUG] initialize_ticket().ticket_id: {:?}", ticket_id);
        println!("[DEBUG] initialize_ticket().price: {}", price);
        println!(
            "[DEBUG] initialize_ticket().ticket_address: {:?}",
            ticket_address
        );
        println!(
            "[DEBUG] initialize_ticket().jackpot_address: {:?}",
            jackpot_address
        );
        println!(
            "[DEBUG] initialize_ticket().chunk_address: {:?}",
            chunk_address
        );
        println!("[DEBUG] initialize_ticket().ticket_end: {:?}", ticket_end);
        println!(
            "[DEBUG] initialize_ticket().current_time: {:?}",
            current_time
        );
        println!(
            "[DEBUG] initialize_ticket().diff (seconds): {:?}",
            ticket_end.saturating_sub(current_time)
        );

        let instruction = Instruction {
            program_id: self.program_id,
            accounts: vec![
                AccountMeta::new(ticket_address, false),
                AccountMeta::new(jackpot_address, false),
                AccountMeta::new(chunk_address, false),
                AccountMeta::new_readonly(self.admin_keypair.pubkey(), true),
                AccountMeta::new(self.admin_keypair.pubkey(), true),
                AccountMeta::new_readonly(solana_sdk::system_program::id(), false),
            ],
            data: {
                let mut data = Vec::with_capacity(8 + 16 + 8 + 4 + 4 + 4 + 8 + 1);
                data.extend_from_slice(&[139, 189, 225, 230, 227, 235, 123, 198]);
                data.extend_from_slice(&ticket_id);
                data.extend_from_slice(&price.to_le_bytes());
                data.extend_from_slice(&ticket_seed.to_le_bytes());
                data.extend_from_slice(&jackpot_seed.to_le_bytes());
                data.extend_from_slice(&first_chunk_seed.to_le_bytes());
                data.extend_from_slice(&ticket_end.to_le_bytes());
                data.extend_from_slice(&[is_test_generate_field as u8]);
                data
            },
        };

        println!("[DEBUG] initialize_ticket().instruction: {:?}", instruction);

        let trx = self.send_transaction(&[instruction]).await?;
        println!("[DEBUG] initialize_ticket().trx: {:?}", trx);

        Ok(trx)
    }

    pub fn find_ticket_address(&self, ticket_id: &[u8; 16], seed: u32) -> (Pubkey, u8) {
        // Convert seed to Uint32Array equivalent
        let mut seed_buffer = vec![0u8; 4];
        seed_buffer.copy_from_slice(&seed.to_le_bytes());

        let (address, bump) =
            Pubkey::find_program_address(&[b"ticket", ticket_id, &seed_buffer], &self.program_id);

        println!("[DEBUG] find_ticket_address:");
        println!("[DEBUG] ticket_id: {:?}", ticket_id);
        println!("[DEBUG] seed: {}", seed);
        println!("[DEBUG] seed_buffer: {:?}", seed_buffer);
        println!("[DEBUG] derived address: {}", address);

        (address, bump)
    }

    pub fn find_jackpot_address(&self, ticket_id: &[u8; 16], seed: u32) -> (Pubkey, u8) {
        let mut seed_buffer = vec![0u8; 4];
        seed_buffer.copy_from_slice(&seed.to_le_bytes());

        Pubkey::find_program_address(&[b"jackpot", ticket_id, &seed_buffer], &self.program_id)
    }

    pub fn find_chunk_address(&self, ticket_id: &[u8; 16], chunk_seed: u32) -> (Pubkey, u8) {
        let mut seed_buffer = vec![0u8; 4];
        seed_buffer.copy_from_slice(&chunk_seed.to_le_bytes());

        Pubkey::find_program_address(
            &[b"participants", ticket_id, &seed_buffer],
            &self.program_id,
        )
    }

    pub async fn set_winner_ticket(
        &self,
        ticket_id: [u8; 16],
        winner_pubkey: Pubkey,
        ticket_seed: u32,
        jackpot_seed: u32,
    ) -> Result<String, TicketServiceError> {
        let (ticket_address, _) = self.find_ticket_address(&ticket_id, ticket_seed);
        let (jackpot_address, _) = self.find_jackpot_address(&ticket_id, jackpot_seed);

        println!("[DEBUG] set_winner_ticket().ticket_id: {:?}", ticket_id);
        println!("[DEBUG] set_winner_ticket().ticket_seed: {:?}", ticket_seed);
        println!(
            "[DEBUG] set_winner_ticket().ticket_address: {:?}",
            ticket_address
        );
        println!(
            "[DEBUG] set_winner_ticket().jackpot_address: {:?}",
            jackpot_address
        );
        println!(
            "[DEBUG] set_winner_ticket().program_id: {:?}",
            self.program_id
        );

        // Проверяем существование аккаунта
        let client_clone = self.rpc_client.clone();
        let account_info =
            tokio::task::spawn_blocking(move || client_clone.get_account(&ticket_address))
                .await
                .unwrap()
                .map_err(|e| {
                    TicketServiceError::InvalidData(format!("Failed to get account info: {}", e))
                })?;

        println!(
            "[DEBUG] set_winner_ticket().account_info: {:?}",
            account_info
        );

        let instruction_result: Result<Instruction, String> = if let Some(instruction) = {
            let temp_instruction = Instruction {
                program_id: self.program_id,
                accounts: vec![
                    AccountMeta::new(ticket_address, false),
                    AccountMeta::new(jackpot_address, false),
                    AccountMeta::new(self.admin_keypair.pubkey(), true),
                    AccountMeta::new_readonly(solana_sdk::system_program::id(), false),
                ],
                data: {
                    let mut data = Vec::with_capacity(8 + 16 + 32);
                    data.extend_from_slice(&[207, 149, 39, 13, 31, 233, 182, 109]);
                    data.extend_from_slice(&ticket_id);
                    data.extend_from_slice(winner_pubkey.as_ref());
                    data
                },
            };

            println!(
                "[DEBUG] set_winner_ticket().temp_instruction {:?}",
                temp_instruction
            );
            println!(
                "[DEBUG] set_winner_ticket().temp_instruction.accounts {:?}",
                temp_instruction.accounts
            );
            println!(
                "[DEBUG] set_winner_ticket().temp_instruction.data {:?}",
                temp_instruction.data
            );

            // Проверяем, что инструкция валидна
            if !temp_instruction.accounts.is_empty() && !temp_instruction.data.is_empty() {
                Some(temp_instruction)
            } else {
                None
            }
        } {
            // Инструкция успешно создана
            println!(
                "[DEBUG] set_winner_ticket().instruction: instruction was created {:?}",
                instruction.clone()
            );
            Ok(instruction)
        } else {
            // Ошибка при создании инструкции
            Err("Failed to create valid instruction".into())
        };

        return self.send_transaction(&[instruction_result.unwrap()]).await;
    }

    pub async fn generate_random(
        &self,
        ticket_id: [u8; 16],
        max_score: i32,
        ticket_seed: u32,
        jackpot_seed: u32,
    ) -> Result<String, TicketServiceError> {
        println!("========== Starting generate_random ==========");
        println!("[DEBUG] Input parameters:");
        println!("[DEBUG] - ticket_id: {:?}", ticket_id);
        println!("[DEBUG] - max_score: {}", max_score);
        println!("[DEBUG] - ticket_seed: {}", ticket_seed);
        println!("[DEBUG] - jackpot_seed: {}", jackpot_seed);

        let (ticket_address, ticket_bump) = self.find_ticket_address(&ticket_id, ticket_seed);
        let (jackpot_address, jackpot_bump) = self.find_jackpot_address(&ticket_id, jackpot_seed);

        println!("[DEBUG] Derived addresses:");
        println!("[DEBUG] - ticket_address: {}", ticket_address);
        println!("[DEBUG] - ticket_bump: {}", ticket_bump);
        println!("[DEBUG] - jackpot_address: {}", jackpot_address);
        println!("[DEBUG] - jackpot_bump: {}", jackpot_bump);

        // Verify accounts exist
        let client_clone = self.rpc_client.clone();
        println!("[DEBUG] Verifying ticket account exists...");
        match tokio::task::spawn_blocking(move || client_clone.get_account(&ticket_address))
            .await
            .unwrap()
        {
            Ok(account) => println!(
                "[DEBUG] Ticket account found with {} lamports",
                account.lamports
            ),
            Err(e) => println!("[WARNING] Failed to fetch ticket account: {}", e),
        }

        let instruction = Instruction {
            program_id: self.program_id,
            accounts: vec![
                AccountMeta::new(ticket_address, false),
                AccountMeta::new(jackpot_address, false),
                AccountMeta::new(self.admin_keypair.pubkey(), true),
                AccountMeta::new_readonly(solana_sdk::system_program::id(), false),
            ],
            data: {
                let mut data = Vec::with_capacity(8 + 16 + 8); // 8 (discriminator) + 16 (ticket_id) + 8 (max_score as u64)
                let discriminator = [221, 81, 206, 124, 154, 50, 197, 164];

                println!("[DEBUG] Building instruction data:");
                println!("[DEBUG] - Discriminator (8 bytes): {:?}", discriminator);
                println!("[DEBUG] - Ticket ID (16 bytes): {:?}", ticket_id);
                println!(
                    "[DEBUG] - Max Score (4 bytes): {:?}",
                    max_score.to_le_bytes()
                );

                data.extend_from_slice(&discriminator);
                data.extend_from_slice(&ticket_id);
                data.extend_from_slice(&(max_score as u64).to_le_bytes()); // Convert to u64 to match Anchor program

                println!("[DEBUG] Final instruction data structure:");
                println!("[DEBUG] - Total length: {} bytes", data.len());
                println!("[DEBUG] - Hex dump: {:02x?}", data);
                println!("[DEBUG] - full data: {:?}", data);

                data
            },
        };

        println!("[DEBUG] Instruction details:");
        println!("[DEBUG] - program_id: {}", instruction.program_id);
        for (i, account) in instruction.accounts.iter().enumerate() {
            println!(
                "[DEBUG] - account[{}]: {} (is_signer: {}, is_writable: {})",
                i, account.pubkey, account.is_signer, account.is_writable
            );
        }

        println!("[DEBUG] Sending transaction...");
        let result = self.send_transaction(&[instruction]).await;
        match &result {
            Ok(signature) => println!(
                "[DEBUG] Transaction successful with signature: {}",
                signature
            ),
            Err(e) => println!("[ERROR] Transaction failed: {:?}", e),
        }

        println!("========== Completed generate_random ==========");
        result
    }

    async fn send_transaction(
        &self,
        instructions: &[Instruction],
    ) -> Result<String, TicketServiceError> {
        let client_clone = self.rpc_client.clone();
        let blockhash = tokio::task::spawn_blocking(move || client_clone.get_latest_blockhash())
            .await
            .unwrap()
            .map_err(|e| {
                TicketServiceError::InvalidData(format!("Не вдалося отримати blockhash: {}", e))
            })?;
        println!("[DEBUG] send_transaction().blockhash: {:?}", blockhash);

        let message = Message::new(instructions, Some(&self.admin_keypair.pubkey()));
        println!("[DEBUG] send_transaction().message: {:?}", message);

        let mut transaction = Transaction::new_unsigned(message);
        transaction.sign(&[&self.admin_keypair], blockhash);
        println!("[DEBUG] send_transaction().transaction: {:?}", transaction);

        let client_clone = self.rpc_client.clone();
        println!("[DEBUG] Sending transaction to Solana network...");

        let config = RpcSendTransactionConfig {
            skip_preflight: true,
            preflight_commitment: None,
            encoding: None,
            max_retries: Some(3),
            min_context_slot: None,
        };

        println!("[DEBUG] Transaction config: {:?}", config);

        let signature_result = tokio::task::spawn_blocking(move || {
            client_clone.send_and_confirm_transaction_with_spinner_and_config(
                &transaction,
                CommitmentConfig {
                    commitment: CommitmentLevel::Confirmed,
                },
                config,
            )
        })
            .await
            .unwrap();

        match &signature_result {
            Ok(signature) => {
                println!(
                    "[DEBUG] Transaction sent successfully! Signature: {}",
                    signature
                );
                Ok(signature.to_string())
            }
            Err(e) => {
                println!("[ERROR] Transaction failed: {:?}", e);

                // Детальный анализ ошибки
                let error_message = e.to_string();
                println!("[ERROR] Full error message: {}", error_message);

                // Безопасное извлечение кода ошибки
                if let Some(error_pos) = error_message.find("custom program error: 0x") {
                    // Безопасное извлечение кода ошибки
                    let start_pos = error_pos + "custom program error: 0x".len();
                    if start_pos < error_message.len() {
                        // Берем только hex-символы
                        let remaining = &error_message[start_pos..];
                        let error_code_hex: String = remaining
                            .chars()
                            .take_while(|c| c.is_ascii_hexdigit())
                            .collect();

                        if !error_code_hex.is_empty() {
                            // Безопасная конвертация hex в decimal
                            if let Ok(error_code_dec) = u32::from_str_radix(&error_code_hex, 16) {
                                println!(
                                    "[ERROR] Custom program error code: 0x{} (decimal: {})",
                                    error_code_hex, error_code_dec
                                );

                                // Список известных ошибок
                                let error_description = match error_code_dec {
                                    3010 => "InvalidEndTime - Неверное время окончания тикета",
                                    3011 => "InvalidPrice - Неверная цена тикета",
                                    3012 => "InvalidTicketId - Неверный ID тикета",
                                    3013 => "TicketIsInactive - Тикет неактивен",
                                    3014 => "TicketIsLocked - Тикет заблокирован",
                                    3015 => "TicketIsCompleted - Тикет уже завершен",
                                    3016 => "InvalidTicketAccount - Неверный аккаунт тикета",
                                    3017 => "InvalidJackpotAccount - Неверный аккаунт джекпота",
                                    3018 => "InvalidChunkAccount - Неверный аккаунт чанка",
                                    // Добавить другие известные коды ошибок по мере необходимости
                                    _ => "Неизвестная ошибка программы",
                                };

                                println!("[ERROR] Error description: {}", error_description);

                                return Err(TicketServiceError::InvalidData(format!(
                                    "Не вдалося отримати signature: Error processing Instruction: custom program error: 0x{} ({}): {}",
                                    error_code_hex, error_code_dec, error_description
                                )));
                            }
                        }
                    }
                }

                Err(TicketServiceError::InvalidData(format!(
                    "Не вдалося отримати signature: {}",
                    e
                )))
            }
        }
    }

    /// Очищает чанк билета
    ///
    /// # Параметры
    /// * `ticket_id` - ID билета (16-байтовый массив)
    /// * `ticket_seed` - Seed билета, используется для поиска адреса аккаунта билета
    /// * `chunk_seed` - Seed чанка, используется для поиска адреса аккаунта чанка, который нужно очистить
    /// * `prev_chunk_seed` - Seed предыдущего чанка (если существует), используется для поиска адреса аккаунта предыдущего чанка
    ///                      Если prev_chunk_seed=0, значит предыдущего чанка нет или его не нужно обновлять
    ///
    /// # Возвращает
    /// * Подпись транзакции в случае успеха
    /// * Ошибку TicketServiceError в случае неудачи
    pub async fn cleanup_chunk(
        &self,
        ticket_id: &[u8; 16],
        ticket_seed: u32,
        chunk_seed: u32,
        prev_chunk_seed: u32,
    ) -> Result<String, TicketServiceError> {
        info!(
            "Cleaning up chunk for ticket {} with ticket_seed={}, chunk_seed={}, prev_chunk_seed={}",
            hex::encode(ticket_id),
            ticket_seed,
            chunk_seed,
            prev_chunk_seed
        );

        // Находим адрес билета
        let (ticket_address, _) = self.find_ticket_address(ticket_id, ticket_seed);
        info!("Ticket address: {}", ticket_address);

        // Находим адрес чанка для очистки
        let (chunk_address, _) = self.find_chunk_address(ticket_id, chunk_seed);
        info!("Chunk address to clean: {}", chunk_address);

        // Проверяем существование аккаунта чанка перед очисткой
        let client_clone = self.rpc_client.clone();
        let chunk_address_clone = chunk_address;

        // Используем blocking_client для блокирующего вызова
        match tokio::task::spawn_blocking(move || client_clone.get_account(&chunk_address_clone))
            .await
            .unwrap()
        {
            Ok(account) => {
                info!(
                    "Account found: size={} bytes, owner={}, lamports={}",
                    account.data.len(),
                    account.owner,
                    account.lamports
                );

                // Если аккаунт существует, но имеет размер <= 8 байт, считаем его уже закрытым
                if account.data.len() <= 8 {
                    info!(
                        "Account data is too small: {} bytes. The account may be already closed.",
                        account.data.len()
                    );

                    // Если у аккаунта всё ещё есть lamports, но нет данных, нужно вернуть lamports
                    if account.lamports > 0 {
                        info!("Account has {} lamports but no data, proceeding with cleanup to reclaim lamports.", account.lamports);
                        // Продолжаем с очисткой
                    } else {
                        // Если аккаунт пустой и без lamports, считаем его уже закрытым
                        info!("Account appears to be already closed (no lamports, no data)");
                        return Ok(format!(
                            "Account already closed or empty: {}",
                            chunk_address
                        ));
                    }
                }
            }
            Err(e) => {
                // Если аккаунт не найден, не пытаемся его очищать
                if e.to_string().contains("AccountNotFound") {
                    info!(
                        "Account {} not found: {}. It may already be closed.",
                        chunk_address, e
                    );
                    return Ok(format!(
                        "Account already closed or not found: {}",
                        chunk_address
                    ));
                } else if e.to_string().contains("account data too small")
                    || e.to_string().contains("0 bytes")
                {
                    info!(
                        "Account {} appears to be already closed or inaccessible: {}",
                        chunk_address, e
                    );
                    return Ok(format!(
                        "Account already closed or inaccessible: {}",
                        chunk_address
                    ));
                }

                // Если ошибка другого типа, логируем и продолжаем (возможно стоит обработать иначе)
                warn!(
                    "Warning when getting chunk account: {}, will proceed with cleanup attempt",
                    e
                );
            }
        };

        // Определяем, нужно ли передавать предыдущий чанк
        let optional_prev_chunk = if prev_chunk_seed != 0 {
            // Находим адрес предыдущего чанка
            let (prev_chunk_address, _) = self.find_chunk_address(ticket_id, prev_chunk_seed);
            info!("Previous chunk address: {}", prev_chunk_address);

            // Проверяем существование предыдущего чанка
            let client_clone = self.rpc_client.clone();
            let prev_address_clone = prev_chunk_address;

            // Блокирующий вызов для проверки существования предыдущего чанка
            let prev_chunk_exists = match tokio::task::spawn_blocking(move || {
                client_clone.get_account(&prev_address_clone)
            })
                .await
                .unwrap()
            {
                Ok(account) => {
                    if account.data.len() <= 8 {
                        info!(
                            "Previous chunk account has too small data: {} bytes. Treating as closed.",
                            account.data.len()
                        );
                        false
                    } else {
                        info!(
                            "Previous chunk account exists: size={} bytes, lamports={}",
                            account.data.len(),
                            account.lamports
                        );
                        true
                    }
                }
                Err(e) => {
                    info!("Previous chunk account not found or error: {}", e);
                    false
                }
            };

            if prev_chunk_exists {
                Some(prev_chunk_address)
            } else {
                None
            }
        } else {
            info!("No previous chunk address (prev_chunk_seed=0)");
            None
        };

        // Создаем список аккаунтов для инструкции
        let mut accounts = vec![
            AccountMeta::new(ticket_address, false), // ticket_account (1)
            AccountMeta::new(chunk_address, false),  // last_chunk (2)
        ];

        // Если есть предыдущий чанк, добавляем его, иначе используем program_id как placeholder
        if let Some(addr) = optional_prev_chunk {
            info!("Adding previous chunk account: {}", addr);
            accounts.push(AccountMeta::new(addr, false)); // prev_chunk (3)
        } else {
            // В структуре CleanupChunk на третьей позиции ожидается Option<AccountInfo>
            // Когда опция None, используем program_id как placeholder (это особенность Anchor)
            info!(
                "Adding program ID as placeholder for missing prev_chunk: {}",
                self.program_id
            );
            accounts.push(AccountMeta::new_readonly(self.program_id, false));
        }

        // Добавляем admin (четвертый аккаунт)
        accounts.push(AccountMeta::new(self.admin_keypair.pubkey(), true)); // admin (4. signer)

        // Добавляем system_program (пятый аккаунт)
        accounts.push(AccountMeta::new_readonly(
            solana_sdk::system_program::id(),
            false,
        )); // system_program (5)

        // Создаем данные инструкции
        const DISCRIMINATOR: [u8; 8] = [224, 136, 42, 214, 187, 221, 79, 0];
        let mut instruction_data = Vec::with_capacity(8 + 16 + 4 + 1 + 4);
        instruction_data.extend_from_slice(&DISCRIMINATOR);
        instruction_data.extend_from_slice(ticket_id);
        instruction_data.extend_from_slice(&chunk_seed.to_le_bytes());

        // Добавляем prev_chunk_seed как Option<u32>
        if optional_prev_chunk.is_some() && prev_chunk_seed > 0 {
            instruction_data.push(1);
            instruction_data.extend_from_slice(&prev_chunk_seed.to_le_bytes());
            info!("Adding prev_chunk_seed: Some({})", prev_chunk_seed);
        } else {
            instruction_data.push(0);
            info!("Adding prev_chunk_seed: None");
        }

        let instruction = Instruction {
            program_id: self.program_id,
            accounts,
            data: instruction_data,
        };

        info!("Sending cleanup_chunk instruction:");
        info!("- Program ID: {}", self.program_id);
        info!("- Number of accounts: {}", instruction.accounts.len());
        info!("- Data length: {} bytes", instruction.data.len());
        for (i, account) in instruction.accounts.iter().enumerate() {
            info!(
                "- Account {}: {} (signer={}, writable={})",
                i, account.pubkey, account.is_signer, account.is_writable
            );
        }

        // Отправляем транзакцию
        match self.send_transaction(&[instruction]).await {
            Ok(signature) => {
                info!("Transaction succeeded with signature: {}", signature);
                Ok(signature)
            }
            Err(e) => {
                // Некоторые ошибки считаем "успешными" для процесса очистки
                let error_str = e.to_string();
                if error_str.contains("AccountNotFound")
                    || error_str.contains("0 bytes")
                    || error_str.contains("account closed")
                    || error_str.contains("ProgramAccountNotFound")
                    || error_str.contains("Custom program error: 0xbc4")
                // InvalidTicketId
                {
                    info!(
                        "Transaction failed because account was already closed: {}",
                        e
                    );
                    Ok(format!("Account already closed: {}", e))
                } else {
                    error!("Transaction failed: {}", e);
                    Err(TicketServiceError::SmartContractError(format!(
                        "Failed to execute cleanup transaction: {}",
                        e
                    )))
                }
            }
        }
    }

    pub async fn cleanup_archived_ticket(
        &self,
        event: TicketChunkCleanupEvent,
        jackpot_seed: u32,
    ) -> Result<String, TicketServiceError> {
        println!("========== Начало cleanup_archived_ticket ==========");
        println!("Входные параметры:");
        println!("- ID билета: {:?}", event.ticket_id);
        println!("- Seed билета: {}", event.ticket_seed);
        println!("- Seed джекпота: {}", jackpot_seed);

        // Получаем адреса аккаунтов
        let (ticket_address, ticket_bump) =
            self.find_ticket_address(&event.ticket_id, event.ticket_seed);
        let (jackpot_address, jackpot_bump) =
            self.find_jackpot_address(&event.ticket_id, jackpot_seed);

        println!("Определены адреса аккаунтов:");
        println!("- Адрес билета: {}", ticket_address);
        println!("- Bump билета: {}", ticket_bump);
        println!("- Адрес джекпота: {}", jackpot_address);
        println!("- Bump джекпота: {}", jackpot_bump);

        // Базовая проверка существования аккаунтов, без десериализации
        let client_clone = self.rpc_client.clone();
        println!("Проверка существования аккаунтов...");

        let accounts_exist = tokio::task::spawn_blocking(move || {
            let ticket_exists = client_clone.get_account(&ticket_address).is_ok();
            let jackpot_exists = client_clone.get_account(&jackpot_address).is_ok();
            (ticket_exists, jackpot_exists)
        })
            .await
            .unwrap_or((false, false));

        match accounts_exist {
            (true, true) => println!("Аккаунты билета и джекпота существуют, продолжаем очистку"),
            (true, false) => {
                println!("Аккаунт билета существует, но джекпота нет. Продолжаем очистку билета")
            }
            (false, true) => {
                println!("Аккаунт джекпота существует, но билета нет. Продолжаем очистку джекпота")
            }
            (false, false) => println!("Оба аккаунта не существуют. Возможно, уже очищены"),
        }

        // Создаем инструкцию для очистки аккаунтов
        println!("Создание инструкции очистки...");
        let cleanup_instruction = Instruction {
            program_id: self.program_id,
            accounts: vec![
                AccountMeta::new(ticket_address, false),
                AccountMeta::new(jackpot_address, false),
                AccountMeta::new(self.admin_keypair.pubkey(), true),
                AccountMeta::new_readonly(solana_sdk::system_program::id(), false),
            ],
            data: {
                let mut data = Vec::with_capacity(8 + 16);
                let discriminator = [70, 245, 206, 243, 244, 163, 128, 235]; // Дискриминатор cleanupArchivedTicket

                data.extend_from_slice(&discriminator);
                data.extend_from_slice(&event.ticket_id);

                println!("Подготовлены данные инструкции:");
                println!("- Длина: {} байт", data.len());
                println!("- ID билета: {:?}", event.ticket_id);
                data
            },
        };

        // Отправляем транзакцию
        println!("Отправка транзакции на очистку архивированного билета...");
        let result = self.send_transaction(&[cleanup_instruction]).await;

        match &result {
            Ok(signature) => {
                println!("✅ Транзакция успешно отправлена! Сигнатура: {}", signature);
                println!("Ожидание подтверждения транзакции...");

                // Ждем немного, чтобы транзакция подтвердилась
                tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;

                // Проверяем статус аккаунтов после транзакции
                let client_clone = self.rpc_client.clone();
                let ticket_addr = ticket_address;
                let jackpot_addr = jackpot_address;

                let post_status = tokio::task::spawn_blocking(move || {
                    let ticket_status = client_clone.get_account(&ticket_addr);
                    let jackpot_status = client_clone.get_account(&jackpot_addr);
                    (ticket_status, jackpot_status)
                })
                    .await
                    .unwrap_or((
                        Err(ClientError::from(io::Error::new(io::ErrorKind::Other, ""))),
                        Err(ClientError::from(io::Error::new(io::ErrorKind::Other, ""))),
                    ));

                match post_status {
                    (Err(_), Err(_)) => println!("✅ Отлично! Оба аккаунта успешно закрыты"),
                    (Ok(_), Err(_)) => println!("⚠️ Аккаунт джекпота закрыт, но билет всё ещё существует"),
                    (Err(_), Ok(_)) => println!("⚠️ Аккаунт билета закрыт, но джекпот всё ещё существует"),
                    (Ok(_), Ok(_)) => println!("⚠️ Оба аккаунта всё ещё существуют, возможно требуется дополнительная очистка"),
                }
            }
            Err(e) => {
                println!("❌ Ошибка при отправке транзакции: {}", e);

                // Проверяем, можем ли мы игнорировать эту ошибку
                if e.to_string().contains("AccountNotFound")
                    || e.to_string().contains("account closed")
                {
                    println!("⚠️ Ошибка может быть проигнорирована - аккаунты уже удалены");
                    return Ok(format!("Аккаунты уже удалены: {}", e));
                }
            }
        }

        println!("========== Завершение cleanup_archived_ticket ==========");
        result.map(|signature| format!("Ticket cleanup completed: {}", signature))
    }

    pub async fn complete_ticket(
        &mut self,
        ticket_id: [u8; 16],
        completed_by: TicketCompletedType,
        ticket_seed: u32,
    ) -> Result<String, TicketServiceError> {
        println!("[DEBUG] ========== НАЧАЛО complete_ticket() ==========");
        println!("[DEBUG] complete_ticket().params:");
        println!("[DEBUG] ticket_id: {:?}", ticket_id);
        println!("[DEBUG] completed_by: {:?}", completed_by);
        println!("[DEBUG] ticket_seed: {}", ticket_seed);
        println!("[DEBUG] admin_keypair public key: {}", self.admin_keypair.pubkey());

        // Находим адрес билета по seed
        let (ticket_address, _) = self.find_ticket_address(&ticket_id, ticket_seed);
        println!("[DEBUG] find_ticket_address:");
        println!("[DEBUG] ticket_id: {:?}", ticket_id);
        println!("[DEBUG] seed: {}", ticket_seed);
        println!("[DEBUG] seed_buffer: {:?}", ticket_seed.to_le_bytes());
        println!("[DEBUG] derived address: {}", ticket_address);

        let completed_by_u8 = match completed_by {
            TicketCompletedType::NONE => 0,
            TicketCompletedType::FIELD => 1,
            TicketCompletedType::EXPIRATION => 2,
        };
        println!("[DEBUG] completed_by converted to u8: {}", completed_by_u8);

        let instr = Instruction {
            program_id: self.program_id,
            accounts: vec![
                AccountMeta::new(ticket_address, false),
                AccountMeta::new(self.admin_keypair.pubkey(), true),
                AccountMeta::new_readonly(solana_sdk::system_program::id(), false),
            ],
            data: {
                let mut data = Vec::with_capacity(8 + 16 + 1);
                let discriminator = [0, 77, 224, 147, 136, 25, 88, 76];
                println!("[DEBUG] Building instruction data:");
                println!("[DEBUG] - Discriminator: {:?}", discriminator);
                println!("[DEBUG] - Ticket ID: {:?}", ticket_id);
                println!("[DEBUG] - Completed By: {} ({:?})", completed_by_u8, completed_by);

                data.extend_from_slice(&discriminator);
                data.extend_from_slice(&ticket_id);
                data.extend_from_slice(&[completed_by_u8]);

                println!("[DEBUG] Final instruction data:");
                println!("[DEBUG] - Length: {} bytes", data.len());
                println!("[DEBUG] - Full data: {:?}", data);
                data
            },
        };

        println!("[DEBUG] Подготовлена инструкция complete_ticket:");
        println!("[DEBUG] - Program ID: {}", self.program_id);
        println!("[DEBUG] - Ticket Account: {}", ticket_address);
        println!("[DEBUG] - Admin Account: {}", self.admin_keypair.pubkey());
        println!("[DEBUG] - System Program: {}", solana_sdk::system_program::id());

        // Выполняем транзакцию
        println!("[DEBUG] Отправка транзакции...");
        let signature = self.send_transaction(&[instr]).await?;
        println!("[DEBUG] Транзакция успешно отправлена: {}", signature);
        println!("[DEBUG] ========== ЗАВЕРШЕНИЕ complete_ticket() ==========");
        
        Ok(signature)
    }

    /// Возвращает аккаунт по публичному ключу
    /// Этот метод используется для получения данных аккаунта из блокчейна
    pub fn get_account(&self, pubkey: &Pubkey) -> Result<Account, ClientError> {
        self.rpc_client.get_account(pubkey)
    }
}
