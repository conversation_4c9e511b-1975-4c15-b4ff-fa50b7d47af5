use std::sync::Arc;

use solana_sdk::pubkey::Pubkey;
use uuid::Uuid as UuidV4;
use tokio::sync::RwLock;


use crate::{
    app::repository_container::RepositoryContainer,
    db::{
        entities::types::*,
        repositories::{
            player_ticket_score_repository::PlayerTicketScoreRepository,
            ticket_repository::TicketRepository,
            user_repository::UserRepository,
            repository::Repository,
        },
    },
    errors::ticket_service_error::TicketServiceError,
    events::jackpot_weighted_random_event::JackpotWeightedRandomValueEvent,
    utils::helpers::get_env_var,
};

use super::ticket_service::TicketService;
use super::smartcontract_service::SmartContractService;

pub struct UserService {
    ticket_repository: TicketRepository,
    smartcontract_service: Arc<RwLock<SmartContractService>>,
    player_ticket_score_repository: PlayerTicketScoreRepository,
    ticket_service: Arc<RwLock<TicketService>>,
    user_repository: UserRepository,
}

impl UserService {
    pub fn new(repository_container: RepositoryContainer,
        smartcontract_service: Arc<RwLock<SmartContractService>>,
        ticket_service: Arc<RwLock<TicketService>>) -> Self {
        Self {
            player_ticket_score_repository: repository_container.player_ticket_score_repository,
            ticket_repository: repository_container.ticket_repository,
            smartcontract_service: smartcontract_service,
            ticket_service,
            user_repository: repository_container.user_repository,
        }
    }

    pub async fn set_winner_by_random(&mut self, event: JackpotWeightedRandomValueEvent) -> Result<(), TicketServiceError> {
        println!("random_number {:?} timestamp {:?}", event.random_number, event.timestamp);
        let ticket_id_string = UuidV4::from_bytes(event.ticket_id).to_string();

        // Получаем билет из базы данных
        let ticket_entity = self.ticket_repository.find_by_id(ticket_id_string.clone()).await?;

        if ticket_entity.is_none() {
            return Err(TicketServiceError::NotFound);
        }

        // Обновляем значение incomeBankAmount
        let mut ticket = ticket_entity.unwrap();
        let ticket_pubkey = ticket.pubkey.clone();
        
        let users = self.player_ticket_score_repository.find_all_users_by_ticket(ticket_pubkey.to_string().as_str()).await?;

        let mut score_sum: u64 = 0;
        for user in users {

            if score_sum < event.random_number && score_sum + user.score as u64 >= event.random_number {
                let trx = self.smartcontract_service.write().await
                    .set_winner_ticket(event.ticket_id, Pubkey::from_str_const(user.user_pubkey.as_str()), ticket.ticket_seed as u32, ticket.jackpot_seed as u32)
                    .await?;
                println!("FINISH_INSTRUCTION: {}", trx);

                let ticket_cloned = ticket.clone();

                ticket.current_state = TicketState::COMPLETED;
                ticket.winner_pub_key = Some(user.user_pubkey.clone());
                ticket.completed_type = Some(TicketCompletedType::EXPIRATION);
                self.ticket_repository.update(ticket).await?;

                if let Some(creator_pub_key) = &ticket_cloned.creator_pub_key {
                    if creator_pub_key == &get_env_var("SERVER_ADMIN_PUBLIC_KEY") {
                        println!("Ticket was created by admin, calling recreate_ticket");

                        self.ticket_service.write().await.recreate_ticket(event.ticket_id).await?;
                        println!("Ticket was created by admin, calling recreate_ticket");
                    }
                } else {
                    eprintln!("❌ Ошибка: creator_pub_key отсутствует у билета {}", ticket_id_string);
                }

                break;
            }

            score_sum += user.score as u64;
        }

        Ok(())
    }

    /// Перевіряє чи існує користувач за публічним ключем
    pub async fn check_user_exists_by_pubkey(&mut self, pubkey: &str) -> Result<bool, TicketServiceError> {
        let user = self.user_repository.find_by_id(pubkey.to_string()).await
            .map_err(|e| TicketServiceError::DatabaseError(e))?;
        
        Ok(user.is_some())
    }

}
