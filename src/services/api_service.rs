use crate::utils::helpers::get_env_var;
use reqwest::{Client, Response};
use std::error::Error;

pub struct ApiService {
    base_url: String,
    client: Client,
    bearer_token: String,
}

impl ApiService {
    pub fn new(base_url: String) -> Self {
        let client = Client::new();
        let bearer_token = String::from("Bearer ") + &get_env_var("NOTIFY_SECRET");

        Self {
            base_url,
            client,
            bearer_token,
        }
    }
    #[allow(dead_code)]
    pub fn change_auth_token(&mut self, token: String) -> &mut Self {
        self.bearer_token = token;
        self
    }

    pub async fn notify_create_ticket(&mut self, json_data: serde_json::Value) -> Result<Response, Box<dyn Error>> {
        let url = format!("{}/api/notify/createdTicket", self.base_url);
        println!("url: {:?}", url);

        let response = self.client
            .post(url)
            .header("Authorization", &self.bearer_token)
            .header("Content-Type", "application/json")
            .json(&json_data)
            .send()
            .await?;

        Ok(response)
    }

    pub async fn purchase_ticket(&mut self, json_data: serde_json::Value) -> Result<Response, Box<dyn Error>> {
        let url = format!("{}/api/notify/buyTicket", self.base_url);
        println!("url: {:?}", url);

        let response = self.client
            .post(url)
            .header("Authorization", &self.bearer_token)
            .header("Content-Type", "application/json")
            .json(&json_data)
            .send()
            .await?;

        Ok(response)
    }

    pub async fn notify_passive_income_withdrawal(&mut self, json_data: serde_json::Value) -> Result<Response, Box<dyn Error>> {
        let url = format!("{}/api/notify/withdrawPassiveIncome", self.base_url);
        println!("notify_passive_income_withdrawal url: {:?}", url);

        let response = self.client
            .post(url)
            .header("Authorization", &self.bearer_token)
            .header("Content-Type", "application/json")
            .json(&json_data)
            .send()
            .await?;

        Ok(response)
    }

    pub async fn notify_jackpot_claim(&mut self, json_data: serde_json::Value) -> Result<Response, Box<dyn Error>> {
        let url = format!("{}/api/notify/jackpotClaim", self.base_url);
        println!("notify_jackpot_claim url: {:?}", url);

        let response = self.client
            .post(url)
            .header("Authorization", &self.bearer_token)
            .header("Content-Type", "application/json")
            .json(&json_data)
            .send()
            .await?;

        Ok(response)
    }

    pub async fn notify_random_created(&mut self, json_data: serde_json::Value) -> Result<Response, Box<dyn Error>> {
        let url = format!("{}/api/notify/randomCreated", self.base_url);
        println!("notify_random_created url: {:?}", url);

        let response = self.client
            .post(url)
            .header("Authorization", &self.bearer_token)
            .header("Content-Type", "application/json")
            .json(&json_data)
            .send()
            .await?;

        Ok(response)
    }

}
