use std::str::FromStr;
use std::sync::Arc;

use chrono::{DateTime, Utc};
use serde_json::json;
use solana_sdk::pubkey::Pubkey;
use tokio::sync::RwLock;
use uuid::Uuid as UuidV4;

use crate::{app::repository_container::RepositoryContainer, db::{entities::{types::{TicketCompletedType, TicketState}, user_field_history::NewUserFieldHistory}, repositories::{ repository::Repository, ticket_repository::TicketRepository, user_field_history_repository::UserFieldHistoryRepository, user_field_repository::UserFieldRepository, user_repository::UserRepository}}, errors::ticket_service_error::TicketServiceError, events::ticket_purchased_event::TicketPurchasedEvent, utils::{game_field::{apply_round, is_winning_field}, helpers::extract_field_from_json}};

use super::{api_service::ApiService, smartcontract_service::SmartContractService};

pub struct FieldService {
    user_field_repository: UserFieldRepository,
    user_field_history_repository: UserFieldHistoryRepository,
    ticket_repository: TicketRepository,
    user_repository: UserRepository,
    smartcontract_service: Arc<RwLock<SmartContractService>>,
    api_service: Arc<RwLock<ApiService>>,
}

impl FieldService {
    pub fn new(repository_container: RepositoryContainer,
        smartcontract_service: Arc<RwLock<SmartContractService>>,
        api_service: Arc<RwLock<ApiService>>) -> Self {
        Self {
            user_field_repository: repository_container.user_field_repository,
            user_field_history_repository: repository_container.user_field_history_repository,
            ticket_repository: repository_container.ticket_repository,
            user_repository: repository_container.user_repository,
            smartcontract_service,
            api_service,
        }
    }

    pub async fn fields_handler(&mut self, event: TicketPurchasedEvent, update_time: DateTime<Utc>, ticket_pubkey: String, transaction_id: i32) -> Result<(), TicketServiceError> {
        let ticket_id = event.ticket_id.clone();
        let ticket_id_uuid = UuidV4::from_bytes(ticket_id);
        let user_id = event.user_id.clone();
        let round_index = event.round_index.clone();
        let round_dir = event.round_dir.clone();
        let round_diff = event.round_diff.clone();

        // Проверяем, является ли новое поле выигрышным
        let is_winner = is_winning_field(&event.user_field);

        println!("[DEBUG] field_service:fields_handler:new_user_field: {}, is_winner: {}", event.user_id, is_winner);

        // Если обнаружен победитель, можно здесь добавить дополнительную логику
        if is_winner {
            self.set_winner(ticket_id, user_id).await?;
            self.repeat_ticket(ticket_id_uuid.to_string()).await;
        } else {
            // Обновляем поля всех пользователей билета
            println!("[DEBUG] field_service:fields_handler:updating_all_user_fields");

            
            // Получаем всех пользователей для данного билета
            let all_user_fields = self.user_field_repository.find_all_by_ticket_id(ticket_pubkey).await?;
            println!("[DEBUG] field_service:fields_handler:found_users_count: {}", all_user_fields.len());

            // Обновляем поле каждого пользователя, кроме того, кто только что купил билет
            for user_field in all_user_fields {
                // Пропускаем текущего пользователя, который только что купил билет
                if user_field.user_pubkey == user_id.to_string() {
                    println!("[DEBUG] field_service:fields_handler:skipping_current_user: {}", user_field.user_pubkey);
                    continue;
                }

                println!("[DEBUG] field_service:fields_handler:updating_user_field: {}", user_field.user_pubkey);

                // Получаем текущее поле пользователя из JSON
                let current_field = match extract_field_from_json(&user_field.current_field) {
                    Ok(field) => field,
                    Err(e) => {
                        eprintln!("❌ [ERROR] Failed to extract field from JSON: {:?}", e);
                        continue;
                    }
                };

                // Применяем раунд к текущему полю
                let new_field = match apply_round(
                    &current_field,
                    round_index,
                    round_dir,
                    round_diff
                ) {
                    Ok(field) => field,
                    Err(e) => {
                        eprintln!("❌ [ERROR] Failed to apply round: {:?}", e);
                        continue;
                    }
                };

                // Преобразуем в JSON
                let new_field_json = serde_json::to_value(&new_field).unwrap_or(json!([8,7,6,5,4,3,2,1,0]));

                println!("[DEBUG] self.user_field_repository.update_current_field");
                // TODO: [DB] Нужно оптимизировать запрос чтобы не вывызывать update_current_field для каждого пользователя
                // Обновляем только current_field, сохраняя исходное inited_field
                self.user_field_repository.update_current_field(new_field_json.clone(), update_time, user_field.user_pubkey.clone(), user_field.ticket_pubkey.clone()).await?;

                println!("[DEBUG] is_winning_field(&new_field)");
                // Проверяем, является ли новое поле выигрышным
                let is_winner = is_winning_field(&new_field);

                println!("[DEBUG] self.user_field_history_repository.create_new");
                // TODO: [DB] Нужно оптимизировать запрос чтобы не вывызывать create_new для каждого пользователя
                // Добавляем запись в историю изменений поля
                self.user_field_history_repository.create_new(NewUserFieldHistory {
                    user_pubkey: user_field.user_pubkey.clone(),
                    ticket_pubkey: user_field.ticket_pubkey.clone(),
                    field_values: new_field_json.clone(),
                    move_object: json!({
                        "index": round_index,
                        "dir": round_dir,
                        "diff": round_diff,
                    }),
                    changed_at: update_time.naive_utc(),
                    transaction_id
                }).await?;

                println!("[DEBUG] field_service:fields_handler:user_field_updated: {}, is_winner: {}", user_field.user_pubkey, is_winner);

                // Если обнаружен победитель, можно здесь добавить дополнительную логику
                if is_winner {
                    let winner_pubkey = Pubkey::from_str(&user_field.user_pubkey)
                        .map_err(|e| TicketServiceError::InvalidData(format!("Invalid pubkey: {}", e)))?;

                    self.set_winner(ticket_id, winner_pubkey).await?;
                    self.repeat_ticket(ticket_id_uuid.to_string()).await;
                }
            }
        }

        Ok(())
    }

    async fn set_winner(&mut self, ticket_id: [u8;16], winner_pubkey: Pubkey) -> Result<(), TicketServiceError> {
        let ticket_id_uuid = UuidV4::from_bytes(ticket_id);
        let winner_pubkey_str = winner_pubkey.to_string();

        println!("[DEBUG] field_service:set_winner:WINNER_FOUND: {}", winner_pubkey_str);

        let now = Utc::now();

        // 1. Claiming ticket data
        let ticket_entity = self.ticket_repository.find_by_id(ticket_id_uuid.to_string()).await?;
        if ticket_entity.is_none() {
            return Err(TicketServiceError::NotFound);
        }

        let mut ticket = ticket_entity.unwrap();

        // 2. Saving jackpot amount
        let jackpot_amount = ticket.main_bank_amount;
        println!("[DEBUG] ticket_seed before(i32) = {}", ticket.ticket_seed);
        let ticket_seed = ticket.ticket_seed as u32;
        println!("[DEBUG] ticket_seed after(u32) = {}", ticket_seed);
        let jackpot_seed = ticket.jackpot_seed as u32;

        // 3. Updating ticket status to completed
        ticket.current_state = TicketState::COMPLETED;
        ticket.winner_pub_key = Some(winner_pubkey_str.clone());
        ticket.completed_type = Some(TicketCompletedType::FIELD);
        ticket.end_time = Some(now.naive_utc());

        // Updating ticket in DB
        self.ticket_repository.update(ticket).await?;

        // 5. Updatin winner data
        let mut winner = self.user_repository.find_by_id(winner_pubkey_str.clone()).await?
            .ok_or_else(|| TicketServiceError::InvalidData("Winner not found".to_string()))?;

        // Incrementing user earnings and combinations won
        winner.total_earnings += jackpot_amount;
        winner.withdrawn_earnings += jackpot_amount;
        winner.perfect_combinations_won += 1;

        // Updating user in DB
        self.user_repository.update(winner).await?;

        // Сначала вызываем complete_ticket для завершения билета
        println!("[DEBUG] field_service:set_winner:CALLING_COMPLETE_TICKET");
        let complete_result = self.smartcontract_service.write().await
            .complete_ticket(ticket_id, TicketCompletedType::FIELD, ticket_seed)
            .await?;
        println!("[DEBUG] field_service:set_winner:COMPLETE_INSTRUCTION_SUCCESS: {}", complete_result);

        // Добавляем задержку для завершения транзакции
        tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;

        // Вызываем обновленный метод set_winner_ticket вместо finish_ticket
        println!("[DEBUG] field_service:set_winner:CALLING_SET_WINNER_TICKET");
        let trx = self.smartcontract_service.write().await
            .set_winner_ticket(ticket_id, winner_pubkey, ticket_seed, jackpot_seed)
            .await?;
        println!("[DEBUG] field_service:set_winner:FINISH_INSTRUCTION_SUCCESS: {}", trx);

        Ok(())
    }

    async fn repeat_ticket(&mut self, ticket_id: String){
        println!("[DEBUG] field_service:set_winner: REPEAT_TICKET");

        // TODO нам тут надо сделать так что бы достали их характеристики старого билета и их применили для создания нового
        // Creating body request for new ticket
        let new_ticket_params = json!({
            "ticket_id": ticket_id,
        });

        // Calling API to create new ticket
        if let Err(e) = self.api_service.write().await.notify_create_ticket(new_ticket_params).await {
            eprintln!("❌ Failed to create new ticket: {:?}", e);
        }

    }
}
