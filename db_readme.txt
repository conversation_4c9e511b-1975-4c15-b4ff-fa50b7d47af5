
brew update
brew install postgresql
brew services start postgresql
psql --version

psql postgres
psql -U `whoami` postgres // mac only


CREATE DATABASE postgres;
\l

CREATE ROLE "postgres.hhjgxwbwdsvklegckumk" WITH LOGIN PASSWORD 'f188G8Gdj0LgH38x';
\du

ALTER USER "postgres.hhjgxwbwdsvklegckumk" WITH CREATEDB;


// don't use before, for sure it's not needed
GRANT ALL PRIVILEGES ON DATABASE postgres TO "postgres.hhjgxwbwdsvklegckumk";
\c postgres
