[package]
name = "be_ticket"
version = "0.1.0"
edition = "2021"

[dependencies]
actix-web = "4.0"
tokio = { version = "1.43.0", features = ["full"] }
serde = { version = "1.0.218", features = ["derive"] }
serde_json = "1.0.139"
futures-util = "0.3"
solana-sdk = "2.2.1"
solana-client = "2.2.0"
base64 = "0.22.1"
borsh = "1.5.5"
rand = "0.9.0"
chrono = { version = "0.4.40", features = ["serde"] }
dotenvy = "0.15"
uuid = { version = "1.15.1", features = ["v4"] }
thiserror = "2.0.11"
reqwest = { version = "0.11.24", features = ["json"] }
r2d2 = "0.8"
sqlx = { version = "0.7.3", features = ["runtime-tokio-native-tls", "postgres", "bigdecimal", "chrono"] }
strum = "0.27.1"
strum_macros = "0.27.1"
log = "0.4"
env_logger = "0.11.2"
hex = "0.4"